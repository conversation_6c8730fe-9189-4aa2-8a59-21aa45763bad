const express = require('express');
const { authenticateToken, optionalAuth } = require('../middleware/auth');
const firestoreService = require('../services/firestoreService');

const router = express.Router();

// @route   POST /api/lists
// @desc    Create a new list (simplified)
// @access  Private
router.post('/', authenticateToken, async (req, res) => {
  try {
    res.json({ message: 'List creation endpoint - coming soon' });
  } catch (error) {
    console.error('Create list error:', error);
    res.status(500).json({ message: 'Server error creating list' });
  }
});

// @route   GET /api/lists
// @desc    Get all lists (simplified)
// @access  Public
router.get('/', optionalAuth, async (req, res) => {
  try {
    const lists = await firestoreService.query('lists', [
      ['visibility', '==', 'public'],
      ['isWatchlist', '==', false]
    ], ['createdAt', 'desc'], 20);

    res.json({
      lists: lists || [],
      pagination: {
        page: 1,
        limit: 20,
        total: lists?.length || 0,
        pages: 1
      }
    });
  } catch (error) {
    console.error('Get lists error:', error);
    res.status(500).json({ message: 'Server error fetching lists' });
  }
});

// @route   GET /api/lists/watchlist
// @desc    Get user's watchlist (simplified)
// @access  Private
router.get('/watchlist', authenticateToken, async (req, res) => {
  try {
    const watchlists = await firestoreService.query('lists', [
      ['userId', '==', req.user.id],
      ['isWatchlist', '==', true]
    ]);

    if (watchlists.length === 0) {
      // Create a default watchlist
      const watchlist = await firestoreService.create('lists', {
        userId: req.user.id,
        name: 'Watchlist',
        description: 'Movies I want to watch',
        visibility: 'private',
        isWatchlist: true,
        movies: [],
        tags: [],
        likes: [],
        comments: [],
        stats: {
          moviesCount: 0,
          likesCount: 0,
          commentsCount: 0
        }
      });
      
      return res.json(watchlist);
    }

    res.json(watchlists[0]);
  } catch (error) {
    console.error('Get watchlist error:', error);
    res.status(500).json({ message: 'Server error fetching watchlist' });
  }
});

module.exports = router;
