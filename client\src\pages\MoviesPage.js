import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { FaFilter, FaTimes } from 'react-icons/fa';
import { api } from '../services/api';
import MovieCard from '../components/UI/MovieCard';
import LoadingSpinner from '../components/UI/LoadingSpinner';

const MoviesPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [movies, setMovies] = useState([]);
  const [genres, setGenres] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [showFilters, setShowFilters] = useState(false);
  
  const [filters, setFilters] = useState({
    filter: searchParams.get('filter') || 'popular',
    genre: searchParams.get('genre') || '',
    year: searchParams.get('year') || '',
    sort: searchParams.get('sort') || 'popularity.desc'
  });
  
  const [pagination, setPagination] = useState({
    page: 1,
    totalPages: 1
  });

  // Fetch genres on mount
  useEffect(() => {
    const fetchGenres = async () => {
      try {
        const response = await api.movies.genres();
        setGenres(response.genres);
      } catch (error) {
        console.error('Error fetching genres:', error);
      }
    };

    fetchGenres();
  }, []);

  // Fetch movies when filters change
  useEffect(() => {
    fetchMovies(1, true);
  }, [filters]);

  const fetchMovies = async (page = 1, reset = false) => {
    if (page === 1) {
      setLoading(true);
    } else {
      setLoadingMore(true);
    }

    try {
      let response;
      
      switch (filters.filter) {
        case 'trending':
          response = await api.movies.trending('week', page);
          break;
        case 'now-playing':
          response = await api.movies.nowPlaying(page);
          break;
        case 'upcoming':
          response = await api.movies.upcoming(page);
          break;
        case 'top-rated':
          response = await api.movies.topRated(page);
          break;
        case 'discover':
          const discoverFilters = {};
          if (filters.genre) discoverFilters.with_genres = filters.genre;
          if (filters.year) discoverFilters.primary_release_year = filters.year;
          if (filters.sort) discoverFilters.sort_by = filters.sort;
          response = await api.movies.discover(discoverFilters, page);
          break;
        default:
          response = await api.movies.popular(page);
      }

      if (reset) {
        setMovies(response.results);
      } else {
        setMovies(prev => [...prev, ...response.results]);
      }

      setPagination({
        page: response.page,
        totalPages: response.total_pages
      });

      setHasMore(response.page < response.total_pages);
    } catch (error) {
      console.error('Error fetching movies:', error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    
    // Update URL params
    const newParams = new URLSearchParams();
    Object.entries(newFilters).forEach(([k, v]) => {
      if (v) newParams.set(k, v);
    });
    setSearchParams(newParams);
  };

  const loadMore = () => {
    if (!loadingMore && hasMore) {
      fetchMovies(pagination.page + 1, false);
    }
  };

  const clearFilters = () => {
    const newFilters = {
      filter: 'popular',
      genre: '',
      year: '',
      sort: 'popularity.desc'
    };
    setFilters(newFilters);
    setSearchParams({});
  };

  const filterOptions = [
    { value: 'popular', label: 'Popular' },
    { value: 'trending', label: 'Trending' },
    { value: 'now-playing', label: 'Now Playing' },
    { value: 'upcoming', label: 'Upcoming' },
    { value: 'top-rated', label: 'Top Rated' },
    { value: 'discover', label: 'Discover' }
  ];

  const sortOptions = [
    { value: 'popularity.desc', label: 'Most Popular' },
    { value: 'popularity.asc', label: 'Least Popular' },
    { value: 'release_date.desc', label: 'Newest First' },
    { value: 'release_date.asc', label: 'Oldest First' },
    { value: 'vote_average.desc', label: 'Highest Rated' },
    { value: 'vote_average.asc', label: 'Lowest Rated' }
  ];

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 50 }, (_, i) => currentYear - i);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold">Films</h1>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="btn-secondary flex items-center"
          >
            <FaFilter className="w-4 h-4 mr-2" />
            Filters
          </button>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="card p-6 mb-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Filter Type */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Category
                </label>
                <select
                  value={filters.filter}
                  onChange={(e) => handleFilterChange('filter', e.target.value)}
                  className="input"
                >
                  {filterOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Genre */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Genre
                </label>
                <select
                  value={filters.genre}
                  onChange={(e) => handleFilterChange('genre', e.target.value)}
                  className="input"
                >
                  <option value="">All Genres</option>
                  {genres.map(genre => (
                    <option key={genre.id} value={genre.id}>
                      {genre.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Year */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Year
                </label>
                <select
                  value={filters.year}
                  onChange={(e) => handleFilterChange('year', e.target.value)}
                  className="input"
                >
                  <option value="">All Years</option>
                  {years.map(year => (
                    <option key={year} value={year}>
                      {year}
                    </option>
                  ))}
                </select>
              </div>

              {/* Sort */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Sort By
                </label>
                <select
                  value={filters.sort}
                  onChange={(e) => handleFilterChange('sort', e.target.value)}
                  className="input"
                  disabled={filters.filter !== 'discover'}
                >
                  {sortOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Clear Filters */}
            <div className="mt-4 flex justify-end">
              <button
                onClick={clearFilters}
                className="btn-ghost text-sm flex items-center"
              >
                <FaTimes className="w-3 h-3 mr-1" />
                Clear Filters
              </button>
            </div>
          </div>
        )}

        {/* Movies Grid */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 mb-8">
          {movies.map((movie) => (
            <MovieCard
              key={movie.id}
              movie={movie}
              showRating
              size="md"
            />
          ))}
        </div>

        {/* Load More */}
        {hasMore && (
          <div className="text-center">
            <button
              onClick={loadMore}
              disabled={loadingMore}
              className="btn-secondary"
            >
              {loadingMore ? (
                <LoadingSpinner size="sm" />
              ) : (
                'Load More'
              )}
            </button>
          </div>
        )}

        {/* No Results */}
        {!loading && movies.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-400 text-lg">No movies found</p>
            <button
              onClick={clearFilters}
              className="btn-primary mt-4"
            >
              Clear Filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default MoviesPage;
