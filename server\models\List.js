const mongoose = require('mongoose');

const listSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    maxlength: 1000,
    trim: true,
    default: ''
  },
  movies: [{
    tmdbId: {
      type: Number,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    posterPath: String,
    releaseDate: String,
    addedAt: {
      type: Date,
      default: Date.now
    },
    notes: {
      type: String,
      maxlength: 500,
      trim: true
    },
    order: {
      type: Number,
      default: 0
    }
  }],
  visibility: {
    type: String,
    enum: ['public', 'private'],
    default: 'public'
  },
  isWatchlist: {
    type: Boolean,
    default: false
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  likes: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    likedAt: {
      type: Date,
      default: Date.now
    }
  }],
  comments: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    text: {
      type: String,
      required: true,
      maxlength: 500,
      trim: true
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  stats: {
    moviesCount: {
      type: Number,
      default: 0
    },
    likesCount: {
      type: Number,
      default: 0
    },
    commentsCount: {
      type: Number,
      default: 0
    }
  },
  coverImage: {
    type: String,
    default: ''
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
listSchema.index({ user: 1, name: 1 });
listSchema.index({ user: 1, isWatchlist: 1 });
listSchema.index({ visibility: 1, createdAt: -1 });
listSchema.index({ 'movies.tmdbId': 1 });
listSchema.index({ 'likes.user': 1 });
listSchema.index({ tags: 1 });

// Text search index
listSchema.index({ 
  name: 'text', 
  description: 'text', 
  tags: 'text' 
});

// Virtual for movies count
listSchema.virtual('moviesCount').get(function() {
  return this.movies.length;
});

// Virtual for likes count
listSchema.virtual('likesCount').get(function() {
  return this.likes.length;
});

// Virtual for comments count
listSchema.virtual('commentsCount').get(function() {
  return this.comments.length;
});

// Pre-save middleware to update stats
listSchema.pre('save', function(next) {
  this.stats.moviesCount = this.movies.length;
  this.stats.likesCount = this.likes.length;
  this.stats.commentsCount = this.comments.length;
  
  // Sort movies by order
  this.movies.sort((a, b) => a.order - b.order);
  
  next();
});

// Method to check if user has liked the list
listSchema.methods.isLikedBy = function(userId) {
  return this.likes.some(like => like.user.toString() === userId.toString());
};

// Method to add like
listSchema.methods.addLike = function(userId) {
  if (!this.isLikedBy(userId)) {
    this.likes.push({ user: userId });
  }
};

// Method to remove like
listSchema.methods.removeLike = function(userId) {
  this.likes = this.likes.filter(like => like.user.toString() !== userId.toString());
};

// Method to add movie to list
listSchema.methods.addMovie = function(movieData) {
  const existingMovie = this.movies.find(movie => movie.tmdbId === movieData.tmdbId);
  if (!existingMovie) {
    const order = this.movies.length > 0 ? Math.max(...this.movies.map(m => m.order)) + 1 : 0;
    this.movies.push({ ...movieData, order });
  }
};

// Method to remove movie from list
listSchema.methods.removeMovie = function(tmdbId) {
  this.movies = this.movies.filter(movie => movie.tmdbId !== tmdbId);
};

// Method to reorder movies
listSchema.methods.reorderMovies = function(movieOrders) {
  movieOrders.forEach(({ tmdbId, order }) => {
    const movie = this.movies.find(m => m.tmdbId === tmdbId);
    if (movie) {
      movie.order = order;
    }
  });
};

module.exports = mongoose.model('List', listSchema);
