import React, { createContext, useContext, useEffect, useState } from 'react';
import { 
  signInWithPopup, 
  signOut, 
  onAuthStateChanged,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword
} from 'firebase/auth';
import { auth, googleProvider } from '../config/firebase';
import { api } from '../services/api';
import toast from 'react-hot-toast';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [authToken, setAuthToken] = useState(null);

  // Sign in with Google
  const signInWithGoogle = async () => {
    try {
      const result = await signInWithPopup(auth, googleProvider);
      const token = await result.user.getIdToken();
      
      // Set token for API requests
      setAuthToken(token);
      api.setAuthToken(token);
      
      // Register/update user in our backend
      const response = await api.post('/auth/register', {
        displayName: result.user.displayName,
        email: result.user.email
      });
      
      setUserProfile(response.data.user);
      toast.success('Successfully signed in!');
      
      return result.user;
    } catch (error) {
      console.error('Google sign in error:', error);
      toast.error('Failed to sign in with Google');
      throw error;
    }
  };

  // Sign in with email and password
  const signInWithEmail = async (email, password) => {
    try {
      const result = await signInWithEmailAndPassword(auth, email, password);
      const token = await result.user.getIdToken();
      
      setAuthToken(token);
      api.setAuthToken(token);
      
      // Get user profile from backend
      const response = await api.get('/auth/me');
      setUserProfile(response.data);
      
      toast.success('Successfully signed in!');
      return result.user;
    } catch (error) {
      console.error('Email sign in error:', error);
      toast.error('Failed to sign in');
      throw error;
    }
  };

  // Sign up with email and password
  const signUpWithEmail = async (email, password, displayName) => {
    try {
      const result = await createUserWithEmailAndPassword(auth, email, password);
      const token = await result.user.getIdToken();
      
      setAuthToken(token);
      api.setAuthToken(token);
      
      // Register user in our backend
      const response = await api.post('/auth/register', {
        displayName,
        email
      });
      
      setUserProfile(response.data.user);
      toast.success('Account created successfully!');
      
      return result.user;
    } catch (error) {
      console.error('Email sign up error:', error);
      toast.error('Failed to create account');
      throw error;
    }
  };

  // Sign out
  const logout = async () => {
    try {
      await signOut(auth);
      setCurrentUser(null);
      setUserProfile(null);
      setAuthToken(null);
      api.clearAuthToken();
      toast.success('Successfully signed out');
    } catch (error) {
      console.error('Sign out error:', error);
      toast.error('Failed to sign out');
    }
  };

  // Update user profile
  const updateProfile = async (profileData) => {
    try {
      const response = await api.put('/auth/profile', profileData);
      setUserProfile(response.data.user);
      toast.success('Profile updated successfully!');
      return response.data.user;
    } catch (error) {
      console.error('Update profile error:', error);
      toast.error('Failed to update profile');
      throw error;
    }
  };

  // Get fresh user profile
  const refreshProfile = async () => {
    try {
      if (authToken) {
        const response = await api.get('/auth/me');
        setUserProfile(response.data);
        return response.data;
      }
    } catch (error) {
      console.error('Refresh profile error:', error);
    }
  };

  // Delete account
  const deleteAccount = async () => {
    try {
      await api.delete('/auth/account');
      await signOut(auth);
      setCurrentUser(null);
      setUserProfile(null);
      setAuthToken(null);
      api.clearAuthToken();
      toast.success('Account deleted successfully');
    } catch (error) {
      console.error('Delete account error:', error);
      toast.error('Failed to delete account');
      throw error;
    }
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setCurrentUser(user);
      
      if (user) {
        try {
          const token = await user.getIdToken();
          setAuthToken(token);
          api.setAuthToken(token);
          
          // Get user profile from backend
          const response = await api.get('/auth/me');
          setUserProfile(response.data);
        } catch (error) {
          console.error('Auth state change error:', error);
          // If backend request fails, clear auth state
          setCurrentUser(null);
          setUserProfile(null);
          setAuthToken(null);
          api.clearAuthToken();
        }
      } else {
        setUserProfile(null);
        setAuthToken(null);
        api.clearAuthToken();
      }
      
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  // Refresh token periodically
  useEffect(() => {
    let interval;
    
    if (currentUser) {
      interval = setInterval(async () => {
        try {
          const token = await currentUser.getIdToken(true);
          setAuthToken(token);
          api.setAuthToken(token);
        } catch (error) {
          console.error('Token refresh error:', error);
        }
      }, 50 * 60 * 1000); // Refresh every 50 minutes
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [currentUser]);

  const value = {
    currentUser,
    userProfile,
    authToken,
    loading,
    signInWithGoogle,
    signInWithEmail,
    signUpWithEmail,
    logout,
    updateProfile,
    refreshProfile,
    deleteAccount
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
