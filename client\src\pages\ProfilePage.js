import React from 'react';
import { useParams } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const ProfilePage = () => {
  const { id } = useParams();
  const { userProfile } = useAuth();

  // If no ID in params, show current user's profile
  const isOwnProfile = !id || (userProfile && id === userProfile.id);

  return (
    <div className="min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 className="text-3xl font-bold mb-8">
          {isOwnProfile ? 'Your Profile' : 'User Profile'}
        </h1>
        {id && <p className="text-gray-400">User ID: {id}</p>}
        <p className="text-gray-400 mt-4">
          This page will show user profile with favorite films, recent reviews, lists, and social stats.
        </p>
      </div>
    </div>
  );
};

export default ProfilePage;
