# 🔧 Troubleshooting Guide

## Common Issues and Solutions

### 1. "This site can't be reached" Error

**Problem**: <PERSON><PERSON><PERSON> shows "This site can't be reached" for localhost:3000 or localhost:5000

**Solutions**:

#### Check if servers are running:
```bash
# Open Command Prompt/PowerShell and run:
netstat -an | findstr :3000
netstat -an | findstr :5000
```

If no output, servers aren't running.

#### Start servers manually:
```bash
# Method 1: Use the batch file (Windows)
start.bat

# Method 2: Use npm commands
npm install
npm run install-all
npm run dev

# Method 3: Start individually
# Terminal 1 (Backend):
cd server
npm install
npm run dev

# Terminal 2 (Frontend):
cd client
npm install
npm start
```

### 2. Port Already in Use

**Error**: `EADDRINUSE: address already in use :::3000` or `:::5000`

**Solution**:
```bash
# Kill processes on ports 3000 and 5000
netstat -ano | findstr :3000
netstat -ano | findstr :5000

# Note the PID and kill it:
taskkill /PID <PID_NUMBER> /F
```

### 3. Dependencies Not Installed

**Error**: `Cannot find module` or `Module not found`

**Solution**:
```bash
# Delete node_modules and reinstall
rmdir /s node_modules
rmdir /s client\node_modules
rmdir /s server\node_modules

# Reinstall everything
npm run install-all
```

### 4. Firebase Connection Issues

**Error**: Firebase authentication or database errors

**Check**:
1. Firebase project is active
2. Authentication is enabled (Google + Email/Password)
3. Firestore database is created
4. Environment variables are correct

**Verify .env files**:
- `server/.env` should have Firebase Admin SDK config
- `client/.env` should have Firebase web config

### 5. TMDB API Issues

**Error**: Movie search not working

**Check**:
- TMDB API key in `server/.env`: `TMDB_API_KEY=cc34bd9ea6ab6d053cbc80f3c568b38c`
- Internet connection
- TMDB service status

### 6. Build/Compilation Errors

**Error**: React build fails or server won't start

**Solution**:
```bash
# Clear npm cache
npm cache clean --force

# Delete package-lock.json files
del package-lock.json
del client\package-lock.json
del server\package-lock.json

# Reinstall
npm run install-all
```

## Step-by-Step Debugging

### 1. Check Project Structure
Your directory should look like this:
```
letterboxd-clone/
├── client/
│   ├── src/
│   ├── public/
│   ├── package.json
│   └── .env
├── server/
│   ├── routes/
│   ├── config/
│   ├── services/
│   ├── middleware/
│   ├── index.js
│   ├── package.json
│   └── .env
├── package.json
├── start.bat
└── README.md
```

### 2. Test Backend Separately
```bash
cd server
npm install
npm run dev
```
Should show: `Server running on port 5000`

### 3. Test Frontend Separately
```bash
cd client
npm install
npm start
```
Should open browser at http://localhost:3000

### 4. Check Environment Variables
**server/.env should contain**:
```
TMDB_API_KEY=cc34bd9ea6ab6d053cbc80f3c568b38c
FIREBASE_PROJECT_ID=letterboxd-clone-957ed
FIREBASE_PRIVATE_KEY_ID=d26ea53e338c72c8534b2688e3c2fdfd894eb6b1
# ... other Firebase config
```

**client/.env should contain**:
```
REACT_APP_FIREBASE_API_KEY=AIzaSyCbvEOhEGxewQUntk0FymKPQV34oIDJ9qs
REACT_APP_FIREBASE_AUTH_DOMAIN=letterboxd-clone-957ed.firebaseapp.com
# ... other Firebase config
```

## Quick Fixes

### Reset Everything:
```bash
# Delete all node_modules
rmdir /s node_modules
rmdir /s client\node_modules  
rmdir /s server\node_modules

# Delete package-lock files
del package-lock.json
del client\package-lock.json
del server\package-lock.json

# Reinstall everything
npm install
npm run install-all

# Start servers
npm run dev
```

### Check if Node.js is installed:
```bash
node --version
npm --version
```
Should show version numbers. If not, install Node.js from https://nodejs.org/

### Windows Firewall:
If Windows Firewall is blocking, allow Node.js through firewall or temporarily disable it for testing.

## Still Having Issues?

1. **Check the terminal output** for specific error messages
2. **Open browser developer tools** (F12) and check console for errors
3. **Verify Firebase project settings** in Firebase Console
4. **Try running on a different port** by changing PORT in server/.env
5. **Restart your computer** (sometimes helps with port conflicts)

## Contact Information

If you're still having issues, please provide:
1. The exact error message
2. Your operating system
3. Node.js version (`node --version`)
4. Terminal output when starting servers

---

**Most common solution**: Delete all node_modules folders and reinstall dependencies! 🔄
