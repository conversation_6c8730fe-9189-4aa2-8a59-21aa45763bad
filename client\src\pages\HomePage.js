import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { FaArrowRight, FaChartLine, FaFire, FaStar } from 'react-icons/fa';
import { api } from '../services/api';
// Authentication removed
import MovieCard from '../components/UI/MovieCard';
import LoadingSpinner from '../components/UI/LoadingSpinner';
import StarRating from '../components/UI/StarRating';

const HomePage = () => {
  const [popularMovies, setPopularMovies] = useState([]);
  const [trendingMovies, setTrendingMovies] = useState([]);
  const [recentReviews, setRecentReviews] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchHomeData = async () => {
      try {
        const [popularRes, trendingRes, reviewsRes] = await Promise.all([
          api.movies.popular(1),
          api.movies.trending('week', 1),
          api.reviews.getAll({ limit: 6, sort: 'newest' })
        ]);

        setPopularMovies(popularRes.results.slice(0, 12));
        setTrendingMovies(trendingRes.results.slice(0, 12));
        setRecentReviews(reviewsRes.reviews);
      } catch (error) {
        console.error('Error fetching home data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchHomeData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-dark-900 via-dark-800 to-dark-900 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              <span className="gradient-text">Track films you've watched.</span>
              <br />
              <span className="text-white">Save those you want to see.</span>
              <br />
              <span className="text-white">Tell your friends what's good.</span>
            </h1>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              The social network for film lovers. Letterboxd lets you share your taste in film.
            </p>
            <Link
              to="/movies"
              className="btn-primary btn-lg inline-flex items-center"
            >
              Explore Movies
              <FaArrowRight className="ml-2 w-4 h-4" />
            </Link>
          </div>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Popular Movies */}
        <section className="mb-16">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-3">
              <FaFire className="w-6 h-6 text-primary-500" />
              <h2 className="text-2xl font-bold">Popular This Week</h2>
            </div>
            <Link
              to="/movies?filter=popular"
              className="btn-ghost text-sm"
            >
              View All
              <FaArrowRight className="ml-2 w-3 h-3" />
            </Link>
          </div>
          
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {popularMovies.map((movie) => (
              <MovieCard
                key={movie.id}
                movie={movie}
                showRating
                size="md"
              />
            ))}
          </div>
        </section>

        {/* Trending Movies */}
        <section className="mb-16">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-3">
              <FaChartLine className="w-6 h-6 text-primary-500" />
              <h2 className="text-2xl font-bold">Trending Now</h2>
            </div>
            <Link
              to="/movies?filter=trending"
              className="btn-ghost text-sm"
            >
              View All
              <FaArrowRight className="ml-2 w-3 h-3" />
            </Link>
          </div>
          
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {trendingMovies.map((movie) => (
              <MovieCard
                key={movie.id}
                movie={movie}
                showRating
                size="md"
              />
            ))}
          </div>
        </section>

        {/* Recent Reviews */}
        <section>
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-3">
              <FaStar className="w-6 h-6 text-primary-500" />
              <h2 className="text-2xl font-bold">Recent Reviews</h2>
            </div>
            <Link
              to="/reviews"
              className="btn-ghost text-sm"
            >
              View All
              <FaArrowRight className="ml-2 w-3 h-3" />
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {recentReviews.map((review) => (
              <div key={review._id} className="card p-6">
                <div className="flex items-start space-x-4">
                  {/* Movie Poster */}
                  <div className="w-16 h-24 bg-dark-700 rounded overflow-hidden flex-shrink-0">
                    {review.moviePosterPath ? (
                      <img
                        src={api.getImageUrl(review.moviePosterPath, 'w154')}
                        alt={review.movieTitle}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <FaStar className="w-4 h-4 text-gray-500" />
                      </div>
                    )}
                  </div>

                  {/* Review Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-2">
                      {review.user.photoURL ? (
                        <img
                          src={review.user.photoURL}
                          alt={review.user.displayName}
                          className="w-6 h-6 rounded-full"
                        />
                      ) : (
                        <div className="w-6 h-6 bg-dark-700 rounded-full" />
                      )}
                      <Link
                        to={`/users/${review.user._id}`}
                        className="text-sm font-medium hover:text-primary-500 transition-colors duration-200"
                      >
                        {review.user.displayName}
                      </Link>
                      <span className="text-xs text-gray-400">
                        {api.formatRelativeTime(review.createdAt)}
                      </span>
                    </div>

                    <Link
                      to={`/movies/${review.tmdbId}`}
                      className="font-semibold hover:text-primary-500 transition-colors duration-200 block mb-2"
                    >
                      {review.movieTitle}
                    </Link>

                    {review.rating && (
                      <div className="flex items-center space-x-2 mb-2">
                        <StarRating rating={review.rating} readonly size="sm" />
                        <span className="text-sm text-gray-400">
                          {review.rating}/5
                        </span>
                      </div>
                    )}

                    {review.reviewText && (
                      <p className="text-sm text-gray-300 line-clamp-3">
                        {review.reviewText}
                      </p>
                    )}

                    <div className="flex items-center justify-between mt-3">
                      <Link
                        to={`/reviews/${review._id}`}
                        className="text-xs text-primary-500 hover:text-primary-400 transition-colors duration-200"
                      >
                        Read full review
                      </Link>
                      <div className="flex items-center space-x-4 text-xs text-gray-400">
                        <span>{review.stats.likesCount} likes</span>
                        <span>{review.stats.commentsCount} comments</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>
      </div>
    </div>
  );
};

export default HomePage;
