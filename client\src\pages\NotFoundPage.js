import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { FaHome, FaFilm } from 'react-icons/fa';

const NotFoundPage = () => {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="mb-8">
          <FaFilm className="w-24 h-24 text-primary-500 mx-auto mb-4" />
          <h1 className="text-6xl font-bold text-white mb-4">404</h1>
          <h2 className="text-2xl font-semibold text-gray-300 mb-2">Page Not Found</h2>
          <p className="text-gray-400 max-w-md mx-auto">
            The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL.
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            to="/"
            className="btn-primary flex items-center justify-center"
          >
            <FaHome className="w-4 h-4 mr-2" />
            Go Home
          </Link>
          <Link
            to="/movies"
            className="btn-secondary flex items-center justify-center"
          >
            <FaFilm className="w-4 h-4 mr-2" />
            Browse Movies
          </Link>
        </div>
      </div>
    </div>
  );
};

export default NotFoundPage;
