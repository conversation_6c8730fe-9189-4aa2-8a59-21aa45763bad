import { initializeApp } from 'firebase/app';
import { getAuth, GoogleAuthProvider } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';

const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY || "AIzaSyCbvEOhEGxewQUntk0FymKPQV34oIDJ9qs",
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN || "letterboxd-clone-957ed.firebaseapp.com",
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID || "letterboxd-clone-957ed",
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET || "letterboxd-clone-957ed.firebasestorage.app",
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID || "************",
  appId: process.env.REACT_APP_FIREBASE_APP_ID || "1:************:web:7be3e2da6ffc34b667a5bb"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Auth
export const auth = getAuth(app);

// Initialize Firestore
export const db = getFirestore(app);

// Google Auth Provider
export const googleProvider = new GoogleAuthProvider();
googleProvider.setCustomParameters({
  prompt: 'select_account'
});

export default app;
