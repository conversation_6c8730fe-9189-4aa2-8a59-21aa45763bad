import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { FaGithub, FaTwitter, FaFilm, FaHeart } from 'react-icons/fa';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-dark-800 border-t border-dark-700 mt-auto">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="col-span-1 md:col-span-2">
            <Link 
              to="/" 
              className="flex items-center space-x-2 text-xl font-bold text-primary-500 mb-4"
            >
              <FaFilm className="w-6 h-6" />
              <span>Letterboxd Clone</span>
            </Link>
            <p className="text-gray-400 text-sm mb-4 max-w-md">
              A social platform for film enthusiasts to discover, rate, and review movies. 
              Built for educational purposes as a demonstration of modern web development.
            </p>
            <div className="flex space-x-4">
              <a
                href="https://github.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-primary-500 transition-colors duration-200"
                aria-label="GitHub"
              >
                <FaGithub className="w-5 h-5" />
              </a>
              <a
                href="https://twitter.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-primary-500 transition-colors duration-200"
                aria-label="Twitter"
              >
                <FaTwitter className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-white font-semibold mb-4">Discover</h3>
            <ul className="space-y-2">
              <li>
                <Link 
                  to="/movies" 
                  className="text-gray-400 hover:text-white transition-colors duration-200 text-sm"
                >
                  Popular Films
                </Link>
              </li>
              <li>
                <Link 
                  to="/movies?filter=trending" 
                  className="text-gray-400 hover:text-white transition-colors duration-200 text-sm"
                >
                  Trending
                </Link>
              </li>
              <li>
                <Link 
                  to="/reviews" 
                  className="text-gray-400 hover:text-white transition-colors duration-200 text-sm"
                >
                  Recent Reviews
                </Link>
              </li>
              <li>
                <Link 
                  to="/lists" 
                  className="text-gray-400 hover:text-white transition-colors duration-200 text-sm"
                >
                  Popular Lists
                </Link>
              </li>
            </ul>
          </div>

          {/* Community */}
          <div>
            <h3 className="text-white font-semibold mb-4">Community</h3>
            <ul className="space-y-2">
              <li>
                <Link 
                  to="/users" 
                  className="text-gray-400 hover:text-white transition-colors duration-200 text-sm"
                >
                  Members
                </Link>
              </li>
              <li>
                <Link 
                  to="/reviews?sort=most_liked" 
                  className="text-gray-400 hover:text-white transition-colors duration-200 text-sm"
                >
                  Top Reviews
                </Link>
              </li>
              <li>
                <Link 
                  to="/lists?sort=most_liked" 
                  className="text-gray-400 hover:text-white transition-colors duration-200 text-sm"
                >
                  Top Lists
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-dark-700 mt-8 pt-8 flex flex-col sm:flex-row justify-between items-center">
          <div className="text-gray-400 text-sm mb-4 sm:mb-0">
            <p className="flex items-center">
              Made with <FaHeart className="w-4 h-4 text-red-500 mx-1" /> for film lovers
            </p>
          </div>
          
          <div className="text-gray-400 text-sm">
            <p>&copy; {currentYear} Letterboxd Clone. Educational project.</p>
          </div>
        </div>

        {/* Disclaimer */}
        <div className="mt-4 p-4 bg-dark-700 rounded-lg">
          <p className="text-xs text-gray-400 text-center">
            This is an educational project created for demonstration purposes. 
            Movie data provided by{' '}
            <a 
              href="https://www.themoviedb.org/" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-primary-500 hover:text-primary-400 transition-colors duration-200"
            >
              The Movie Database (TMDB)
            </a>
            . This product uses the TMDB API but is not endorsed or certified by TMDB.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
