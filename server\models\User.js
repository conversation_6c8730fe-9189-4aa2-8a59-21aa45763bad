const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  firebaseUid: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  displayName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50
  },
  username: {
    type: String,
    unique: true,
    sparse: true,
    trim: true,
    lowercase: true,
    minlength: 3,
    maxlength: 20,
    match: /^[a-zA-Z0-9_]+$/
  },
  bio: {
    type: String,
    maxlength: 500,
    default: ''
  },
  photoURL: {
    type: String,
    default: ''
  },
  emailVerified: {
    type: Boolean,
    default: false
  },
  favoriteFilms: [{
    tmdbId: {
      type: Number,
      required: true
    },
    title: String,
    posterPath: String,
    releaseDate: String
  }],
  followers: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  following: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  watchedFilms: [{
    tmdbId: {
      type: Number,
      required: true
    },
    watchedAt: {
      type: Date,
      default: Date.now
    }
  }],
  stats: {
    totalFilmsWatched: {
      type: Number,
      default: 0
    },
    totalReviews: {
      type: Number,
      default: 0
    },
    totalLists: {
      type: Number,
      default: 0
    },
    followersCount: {
      type: Number,
      default: 0
    },
    followingCount: {
      type: Number,
      default: 0
    }
  },
  preferences: {
    profileVisibility: {
      type: String,
      enum: ['public', 'private'],
      default: 'public'
    },
    showRatings: {
      type: Boolean,
      default: true
    },
    showWatchlist: {
      type: Boolean,
      default: true
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastActive: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
userSchema.index({ displayName: 'text', username: 'text' });
userSchema.index({ 'favoriteFilms.tmdbId': 1 });
userSchema.index({ 'watchedFilms.tmdbId': 1 });
userSchema.index({ followers: 1 });
userSchema.index({ following: 1 });

// Virtual for follower count
userSchema.virtual('followerCount').get(function() {
  return this.followers.length;
});

// Virtual for following count
userSchema.virtual('followingCount').get(function() {
  return this.following.length;
});

// Pre-save middleware to update stats
userSchema.pre('save', function(next) {
  this.stats.followersCount = this.followers.length;
  this.stats.followingCount = this.following.length;
  next();
});

module.exports = mongoose.model('User', userSchema);
