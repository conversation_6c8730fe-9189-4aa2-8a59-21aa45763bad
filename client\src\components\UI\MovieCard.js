import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { FaPlus, Fa<PERSON>heck, FaHeart, FaEye } from 'react-icons/fa';
import { api } from '../../services/api';
import { useAuth } from '../../context/AuthContext';
import StarRating from './StarRating';
import toast from 'react-hot-toast';

const MovieCard = ({ 
  movie, 
  showRating = false, 
  showWatchlistButton = true,
  className = '',
  size = 'md'
}) => {
  const { currentUser } = useAuth();
  const [isInWatchlist, setIsInWatchlist] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const sizeClasses = {
    sm: 'w-32',
    md: 'w-40',
    lg: 'w-48',
    xl: 'w-56'
  };

  const handleWatchlistToggle = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (!currentUser) {
      toast.error('Please sign in to add movies to your watchlist');
      return;
    }

    setIsLoading(true);
    try {
      if (isInWatchlist) {
        await api.lists.removeMovie('watchlist', movie.id);
        setIsInWatchlist(false);
        toast.success('Removed from watchlist');
      } else {
        await api.lists.addMovie('watchlist', {
          tmdbId: movie.id,
          title: movie.title,
          posterPath: movie.posterPath,
          releaseDate: movie.releaseDate
        });
        setIsInWatchlist(true);
        toast.success('Added to watchlist');
      }
    } catch (error) {
      console.error('Watchlist toggle error:', error);
      toast.error('Failed to update watchlist');
    } finally {
      setIsLoading(false);
    }
  };

  const posterUrl = movie.posterPath 
    ? api.getImageUrl(movie.posterPath, 'w500')
    : null;

  const releaseYear = movie.releaseDate 
    ? new Date(movie.releaseDate).getFullYear()
    : null;

  return (
    <div className={`group relative ${sizeClasses[size]} ${className}`}>
      <Link to={`/movies/${movie.id}`} className="block">
        <div className="movie-poster group-hover:scale-105 transition-transform duration-300">
          {posterUrl ? (
            <img
              src={posterUrl}
              alt={movie.title}
              className="w-full h-full object-cover"
              loading="lazy"
            />
          ) : (
            <div className="w-full h-full bg-dark-700 flex items-center justify-center">
              <span className="text-gray-500 text-sm text-center p-2">
                No Image
              </span>
            </div>
          )}
          
          {/* Overlay on hover */}
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-60 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
            <div className="flex space-x-2">
              {showWatchlistButton && currentUser && (
                <button
                  onClick={handleWatchlistToggle}
                  disabled={isLoading}
                  className={`p-2 rounded-full transition-all duration-200 ${
                    isInWatchlist
                      ? 'bg-primary-500 text-dark-900'
                      : 'bg-dark-800 text-white hover:bg-dark-700'
                  }`}
                  title={isInWatchlist ? 'Remove from watchlist' : 'Add to watchlist'}
                >
                  {isLoading ? (
                    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  ) : isInWatchlist ? (
                    <FaCheck className="w-4 h-4" />
                  ) : (
                    <FaPlus className="w-4 h-4" />
                  )}
                </button>
              )}
              
              <button
                className="p-2 bg-dark-800 text-white rounded-full hover:bg-dark-700 transition-colors duration-200"
                title="Mark as watched"
              >
                <FaEye className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </Link>

      {/* Movie info */}
      <div className="mt-2 space-y-1">
        <Link 
          to={`/movies/${movie.id}`}
          className="block hover:text-primary-500 transition-colors duration-200"
        >
          <h3 className="font-medium text-sm leading-tight line-clamp-2">
            {movie.title}
          </h3>
        </Link>
        
        {releaseYear && (
          <p className="text-xs text-gray-400">
            {releaseYear}
          </p>
        )}

        {showRating && movie.voteAverage > 0 && (
          <div className="flex items-center space-x-1">
            <StarRating 
              rating={movie.voteAverage / 2} 
              readonly 
              size="sm"
            />
            <span className="text-xs text-gray-400">
              ({movie.voteCount})
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default MovieCard;
