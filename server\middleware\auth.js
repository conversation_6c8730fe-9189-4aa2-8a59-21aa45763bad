const { auth } = require('../config/firebase');
const firestoreService = require('../services/firestoreService');

const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({ message: 'Access token required' });
    }

    // Verify Firebase token
    const decodedToken = await auth.verifyIdToken(token);

    // Find or create user in Firestore
    let user = await firestoreService.getById('users', decodedToken.uid);

    if (!user) {
      // Create new user if doesn't exist
      const userData = {
        firebaseUid: decodedToken.uid,
        email: decodedToken.email,
        displayName: decodedToken.name || decodedToken.email.split('@')[0],
        photoURL: decodedToken.picture || '',
        emailVerified: decodedToken.email_verified || false,
        bio: '',
        username: '',
        favoriteFilms: [],
        followers: [],
        following: [],
        watchedFilms: [],
        stats: {
          totalFilmsWatched: 0,
          totalReviews: 0,
          totalLists: 0,
          followersCount: 0,
          followingCount: 0
        },
        preferences: {
          profileVisibility: 'public',
          showRatings: true,
          showWatchlist: true
        },
        isActive: true,
        lastActive: firestoreService.serverTimestamp()
      };

      user = await firestoreService.createWithId('users', decodedToken.uid, userData);
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(403).json({ message: 'Invalid or expired token' });
  }
};

const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const decodedToken = await auth.verifyIdToken(token);
      const user = await firestoreService.getById('users', decodedToken.uid);
      req.user = user;
    }
    
    next();
  } catch (error) {
    // Continue without authentication for optional auth
    next();
  }
};

module.exports = { authenticateToken, optionalAuth };
