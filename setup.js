#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

async function setupEnvironment() {
  console.log('🎬 Welcome to Letterboxd Clone Setup!\n');
  console.log('This script will help you configure your environment variables.\n');

  // TMDB API Key (already provided)
  const tmdbApiKey = 'cc34bd9ea6ab6d053cbc80f3c568b38c';
  console.log('✅ TMDB API Key: Already configured\n');

  // Firebase Configuration
  console.log('🔥 Firebase Configuration:');
  console.log('Please provide your Firebase configuration values:\n');

  const firebaseProjectId = await question('Firebase Project ID: ');
  const firebaseApiKey = await question('Firebase API Key (for web app): ');
  const firebaseAuthDomain = await question('Firebase Auth Domain (usually project-id.firebaseapp.com): ');
  const firebaseStorageBucket = await question('Firebase Storage Bucket (usually project-id.appspot.com): ');
  const firebaseMessagingSenderId = await question('Firebase Messaging Sender ID: ');
  const firebaseAppId = await question('Firebase App ID: ');

  console.log('\n🔐 Firebase Admin SDK Configuration:');
  console.log('Please provide your Firebase service account values:\n');

  const firebasePrivateKeyId = await question('Firebase Private Key ID: ');
  const firebaseClientEmail = await question('Firebase Client Email: ');
  const firebaseClientId = await question('Firebase Client ID: ');
  
  console.log('\nFirebase Private Key (paste the entire key including BEGIN/END lines):');
  const firebasePrivateKey = await question('');

  // Create server .env file
  const serverEnvContent = `# TMDB API Configuration
TMDB_API_KEY=${tmdbApiKey}
TMDB_BASE_URL=https://api.themoviedb.org/3

# Firebase Admin SDK Configuration (for backend)
FIREBASE_PROJECT_ID=${firebaseProjectId}
FIREBASE_PRIVATE_KEY_ID=${firebasePrivateKeyId}
FIREBASE_PRIVATE_KEY="${firebasePrivateKey.replace(/\n/g, '\\n')}"
FIREBASE_CLIENT_EMAIL=${firebaseClientEmail}
FIREBASE_CLIENT_ID=${firebaseClientId}
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

# Server Configuration
PORT=5000
NODE_ENV=development
`;

  // Create client .env file
  const clientEnvContent = `# Firebase Configuration (for frontend)
REACT_APP_FIREBASE_API_KEY=${firebaseApiKey}
REACT_APP_FIREBASE_AUTH_DOMAIN=${firebaseAuthDomain}
REACT_APP_FIREBASE_PROJECT_ID=${firebaseProjectId}
REACT_APP_FIREBASE_STORAGE_BUCKET=${firebaseStorageBucket}
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=${firebaseMessagingSenderId}
REACT_APP_FIREBASE_APP_ID=${firebaseAppId}

# API URLs
REACT_APP_API_URL=http://localhost:5000/api
`;

  // Write .env files
  fs.writeFileSync(path.join(__dirname, 'server', '.env'), serverEnvContent);
  fs.writeFileSync(path.join(__dirname, 'client', '.env'), clientEnvContent);
  fs.writeFileSync(path.join(__dirname, '.env'), serverEnvContent);

  console.log('\n✅ Environment files created successfully!');
  console.log('\nFiles created:');
  console.log('- server/.env');
  console.log('- client/.env');
  console.log('- .env (root)');

  console.log('\n🚀 Next steps:');
  console.log('1. Run: npm run install-all');
  console.log('2. Run: npm run dev');
  console.log('3. Open http://localhost:3000 in your browser');

  console.log('\n📚 Need help?');
  console.log('- Check the README.md file for detailed instructions');
  console.log('- Make sure your Firebase project has Authentication and Firestore enabled');

  rl.close();
}

if (require.main === module) {
  setupEnvironment().catch(console.error);
}

module.exports = { setupEnvironment };
