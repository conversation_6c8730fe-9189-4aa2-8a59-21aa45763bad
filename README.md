# Letterboxd Clone - Social Film Discovery Platform

A full-stack web application that replicates the core functionality of Letterboxd, built with React, Node.js, Express, MongoDB, and Firebase Auth. This educational project demonstrates modern web development practices and provides a platform for film enthusiasts to discover, rate, and review movies.

## 🎬 Features

### Core Functionality
- **User Authentication**: Google OAuth and email/password authentication via Firebase
- **Movie Discovery**: Search, browse, and filter movies using TMDB API
- **Reviews & Ratings**: Rate movies (1-5 stars with half increments) and write detailed reviews
- **Lists & Watchlists**: Create custom movie lists and manage personal watchlists
- **Social Features**: Follow users, like reviews/lists, comment on content
- **Activity Feed**: See updates from followed users
- **User Profiles**: Customizable profiles with favorite films and statistics

### Technical Features
- **Responsive Design**: Mobile-first design with Tailwind CSS
- **Real-time Updates**: Live activity feeds and notifications
- **Image Optimization**: Lazy loading and responsive images
- **Search Autocomplete**: Real-time movie search with suggestions
- **Infinite Scrolling**: Smooth pagination for large datasets
- **Dark Theme**: Letterboxd-inspired dark UI with green accents

## 🛠 Tech Stack

### Frontend
- **React 18** - UI library with hooks and context
- **React Router 6** - Client-side routing
- **Tailwind CSS** - Utility-first CSS framework
- **Firebase Auth** - Authentication service
- **Axios** - HTTP client for API requests
- **React Hot Toast** - Toast notifications
- **React Icons** - Icon library

### Backend
- **Node.js** - JavaScript runtime
- **Express.js** - Web application framework
- **Firebase Firestore** - NoSQL cloud database
- **Firebase Admin** - Server-side Firebase integration
- **TMDB API** - Movie data source

### Development Tools
- **Concurrently** - Run multiple commands simultaneously
- **Nodemon** - Auto-restart server during development
- **ESLint** - Code linting
- **Prettier** - Code formatting

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- Firebase project with Authentication and Firestore enabled
- TMDB API key (already configured: cc34bd9ea6ab6d053cbc80f3c568b38c)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd letterboxd-clone
   ```

2. **Install dependencies**
   ```bash
   npm run install-all
   ```

3. **Set up environment variables**

   **Option A: Use the setup script (Recommended)**
   ```bash
   node setup.js
   ```

   **Option B: Manual setup**
   Copy `.env.example` to `.env` and fill in your configuration:
   ```bash
   cp .env.example .env
   ```

4. **Set up Firebase (if not using setup script)**

   **Create Firebase Project:**
   1. Go to [Firebase Console](https://console.firebase.google.com/)
   2. Create a new project
   3. Enable Authentication with Google and Email/Password providers
   4. Enable Firestore Database in test mode
   5. Get your web app configuration
   6. Generate a service account key for server-side usage

   **TMDB API:**
   - Already configured with key: `cc34bd9ea6ab6d053cbc80f3c568b38c`

5. **Start the development servers**
   ```bash
   npm run dev
   ```

   This will start both the backend server (port 5000) and frontend development server (port 3000).

   **First time setup checklist:**
   - ✅ TMDB API key configured
   - ⏳ Firebase project created
   - ⏳ Firebase Authentication enabled (Google + Email/Password)
   - ⏳ Firestore Database enabled
   - ⏳ Environment variables configured
   - ⏳ Dependencies installed

### Development Scripts

```bash
# Start both frontend and backend in development mode
npm run dev

# Start only the backend server
npm run server

# Start only the frontend development server
npm run client

# Build the frontend for production
npm run build

# Install dependencies for both frontend and backend
npm run install-all

# Install only backend dependencies
npm run install-server

# Install only frontend dependencies
npm run install-client
```

## 📁 Project Structure

```
letterboxd-clone/
├── client/                 # React frontend
│   ├── public/            # Static files
│   ├── src/
│   │   ├── components/    # Reusable React components
│   │   ├── context/       # React Context providers
│   │   ├── hooks/         # Custom React hooks
│   │   ├── pages/         # Page components
│   │   ├── services/      # API services
│   │   └── config/        # Configuration files
│   ├── package.json
│   └── tailwind.config.js
├── server/                # Node.js backend
│   ├── config/           # Database and Firebase config
│   ├── middleware/       # Express middleware
│   ├── models/          # Mongoose models
│   ├── routes/          # API routes
│   ├── services/        # Business logic services
│   ├── index.js         # Server entry point
│   └── package.json
├── .env.example         # Environment variables template
├── .gitignore
├── package.json         # Root package.json for scripts
└── README.md
```

## 🔧 Configuration

### Firestore Collections
- **users**: User profiles, authentication, and social connections
- **reviews**: Movie reviews with ratings and comments
- **lists**: Custom movie lists and watchlists
- **activities**: User activity tracking for feeds

### API Endpoints
- **Auth**: `/api/auth/*` - Authentication and user management
- **Movies**: `/api/movies/*` - TMDB movie data integration
- **Reviews**: `/api/reviews/*` - Review CRUD operations
- **Lists**: `/api/lists/*` - List management
- **Users**: `/api/users/*` - User profiles and data
- **Social**: `/api/social/*` - Social features (follow, comments, feed)

## 🚀 Deployment

### Frontend (Vercel)
1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Backend (Render/Railway/Heroku)
1. Create a new web service
2. Connect your GitHub repository
3. Set environment variables
4. Deploy with automatic builds

### Database (Firebase Firestore)
1. Firestore is automatically configured with your Firebase project
2. No additional database setup required
3. Security rules are handled by Firebase

## 🤝 Contributing

This is an educational project, but contributions are welcome! Please:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is for educational purposes only. Movie data is provided by TMDB but this project is not endorsed by TMDB.

## 🙏 Acknowledgments

- [The Movie Database (TMDB)](https://www.themoviedb.org/) for movie data
- [Letterboxd](https://letterboxd.com/) for design inspiration
- [Firebase](https://firebase.google.com/) for authentication services
- [Tailwind CSS](https://tailwindcss.com/) for styling framework

## 📞 Support

If you encounter any issues or have questions:

1. Check the existing issues on GitHub
2. Create a new issue with detailed information
3. Include error messages and steps to reproduce

---

**Note**: This project is created for educational purposes to demonstrate modern web development practices. It is not affiliated with or endorsed by Letterboxd.
