import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaSearch, FaTimes } from 'react-icons/fa';
import { api } from '../../services/api';
import { useDebounce } from '../../hooks/useDebounce';
import LoadingSpinner from '../UI/LoadingSpinner';

const SearchBar = ({ placeholder = 'Search movies...', className = '' }) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  
  const navigate = useNavigate();
  const searchRef = useRef(null);
  const resultsRef = useRef(null);
  
  const debouncedQuery = useDebounce(query, 300);

  // Search for movies
  useEffect(() => {
    const searchMovies = async () => {
      if (!debouncedQuery.trim()) {
        setResults([]);
        setShowResults(false);
        return;
      }

      setIsLoading(true);
      try {
        const response = await api.movies.search(debouncedQuery, 1);
        setResults(response.results.slice(0, 8)); // Limit to 8 results
        setShowResults(true);
        setSelectedIndex(-1);
      } catch (error) {
        console.error('Search error:', error);
        setResults([]);
      } finally {
        setIsLoading(false);
      }
    };

    searchMovies();
  }, [debouncedQuery]);

  // Handle keyboard navigation
  const handleKeyDown = (e) => {
    if (!showResults || results.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < results.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0) {
          handleResultClick(results[selectedIndex]);
        } else if (query.trim()) {
          handleSearch();
        }
        break;
      case 'Escape':
        setShowResults(false);
        setSelectedIndex(-1);
        searchRef.current?.blur();
        break;
    }
  };

  // Handle search submission
  const handleSearch = () => {
    if (query.trim()) {
      navigate(`/search?q=${encodeURIComponent(query.trim())}`);
      setShowResults(false);
      setQuery('');
      searchRef.current?.blur();
    }
  };

  // Handle result click
  const handleResultClick = (movie) => {
    navigate(`/movies/${movie.id}`);
    setShowResults(false);
    setQuery('');
    searchRef.current?.blur();
  };

  // Clear search
  const handleClear = () => {
    setQuery('');
    setResults([]);
    setShowResults(false);
    setSelectedIndex(-1);
    searchRef.current?.focus();
  };

  // Close results when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        searchRef.current && 
        !searchRef.current.contains(event.target) &&
        resultsRef.current &&
        !resultsRef.current.contains(event.target)
      ) {
        setShowResults(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <FaSearch className="w-4 h-4 text-gray-400" />
        </div>
        
        <input
          ref={searchRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => {
            if (results.length > 0) setShowResults(true);
          }}
          placeholder={placeholder}
          className="input pl-10 pr-10"
        />

        {/* Clear/Loading Button */}
        <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
          {isLoading ? (
            <LoadingSpinner size="sm" />
          ) : query && (
            <button
              onClick={handleClear}
              className="text-gray-400 hover:text-white transition-colors duration-200"
            >
              <FaTimes className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Search Results */}
      {showResults && results.length > 0 && (
        <div 
          ref={resultsRef}
          className="absolute top-full left-0 right-0 mt-1 bg-dark-800 border border-dark-600 rounded-lg shadow-dark-lg z-50 max-h-96 overflow-y-auto"
        >
          {results.map((movie, index) => (
            <button
              key={movie.id}
              onClick={() => handleResultClick(movie)}
              className={`w-full flex items-center space-x-3 p-3 text-left hover:bg-dark-700 transition-colors duration-200 ${
                index === selectedIndex ? 'bg-dark-700' : ''
              }`}
            >
              {/* Movie Poster */}
              <div className="w-12 h-16 bg-dark-700 rounded overflow-hidden flex-shrink-0">
                {movie.posterPath ? (
                  <img
                    src={api.getImageUrl(movie.posterPath, 'w92')}
                    alt={movie.title}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <FaSearch className="w-4 h-4 text-gray-500" />
                  </div>
                )}
              </div>

              {/* Movie Info */}
              <div className="flex-1 min-w-0">
                <h3 className="font-medium text-white truncate">
                  {movie.title}
                </h3>
                <p className="text-sm text-gray-400">
                  {movie.releaseDate ? new Date(movie.releaseDate).getFullYear() : 'Unknown'}
                </p>
                {movie.overview && (
                  <p className="text-xs text-gray-500 line-clamp-2 mt-1">
                    {movie.overview}
                  </p>
                )}
              </div>
            </button>
          ))}

          {/* View All Results */}
          {query.trim() && (
            <button
              onClick={handleSearch}
              className="w-full p-3 text-center text-primary-500 hover:bg-dark-700 transition-colors duration-200 border-t border-dark-600"
            >
              View all results for "{query}"
            </button>
          )}
        </div>
      )}

      {/* No Results */}
      {showResults && !isLoading && query.trim() && results.length === 0 && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-dark-800 border border-dark-600 rounded-lg shadow-dark-lg z-50 p-4 text-center">
          <p className="text-gray-400">No movies found for "{query}"</p>
        </div>
      )}
    </div>
  );
};

export default SearchBar;
