# 🔥 Firebase Setup Guide

Follow these steps to set up Firebase for your Letterboxd clone:

## Step 1: Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or "Add project"
3. Enter project name: `letterboxd-clone` (or any name you prefer)
4. **Disable Google Analytics** (not needed for this project)
5. Click "Create project"
6. Wait for project creation to complete

## Step 2: Enable Authentication

1. In your Firebase project dashboard, click **"Authentication"** in the left sidebar
2. Click **"Get started"**
3. Go to **"Sign-in method"** tab
4. Enable **"Google"** provider:
   - Click on Google
   - Toggle "Enable"
   - Click "Save"
5. Enable **"Email/Password"** provider:
   - Click on Email/Password
   - Toggle "Enable" for the first option (Email/Password)
   - Click "Save"

## Step 3: Enable Firestore Database

1. Click **"Firestore Database"** in the left sidebar
2. Click **"Create database"**
3. Choose **"Start in test mode"** (we'll secure it later)
4. Select a location close to you (e.g., us-central1)
5. Click **"Done"**

## Step 4: Get Web App Configuration

1. Go to **Project Settings** (gear icon next to "Project Overview")
2. Scroll down to **"Your apps"** section
3. Click the **web icon** `</>`
4. Enter app nickname: `letterboxd-web`
5. **Don't check** "Firebase Hosting" for now
6. Click **"Register app"**
7. **Copy the configuration object** that looks like this:

```javascript
const firebaseConfig = {
  apiKey: "AIza...",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "*********",
  appId: "1:*********:web:abcdef"
};
```

**Save these values - you'll need them!**

## Step 5: Generate Service Account Key

1. Still in **Project Settings**, go to **"Service accounts"** tab
2. Click **"Generate new private key"**
3. Click **"Generate key"** in the popup
4. A JSON file will download - **keep this file secure!**
5. Open the JSON file and note these values:
   - `project_id`
   - `private_key_id`
   - `private_key` (the long key with BEGIN/END lines)
   - `client_email`
   - `client_id`

## Step 6: Configure Environment Variables

Now you have all the information needed! You can either:

### Option A: Use the setup script (Recommended)
```bash
npm run setup
```

### Option B: Manual configuration
Create `.env` files with the values you collected above.

## Step 7: Test Your Setup

1. Install dependencies:
   ```bash
   npm run install-all
   ```

2. Start the development server:
   ```bash
   npm run dev
   ```

3. Open http://localhost:3000
4. Try signing up with Google or email/password

## Security Rules (Optional - for production)

If you want to secure your Firestore database, go to **Firestore Database** → **Rules** and replace the default rules with:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Anyone can read public reviews
    match /reviews/{reviewId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    // Anyone can read public lists
    match /lists/{listId} {
      allow read: if resource.data.visibility == 'public';
      allow write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    // Users can read activities from people they follow
    match /activities/{activityId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
  }
}
```

## Troubleshooting

**Common Issues:**

1. **"Firebase project not found"** - Check your project ID in the config
2. **"Auth domain not authorized"** - Make sure you're using the correct auth domain
3. **"Permission denied"** - Check that Authentication is enabled
4. **"Firestore not enabled"** - Make sure you created the Firestore database

**Need Help?**
- Check the [Firebase Documentation](https://firebase.google.com/docs)
- Make sure all services (Auth + Firestore) are enabled
- Verify your environment variables are correct

---

Once you complete these steps, provide me with your Firebase configuration values and I'll help you get everything running! 🚀
