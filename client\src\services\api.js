import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

class ApiService {
  constructor() {
    this.client = axios.create({
      baseURL: API_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Add auth token if available
        const token = this.getStoredToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          this.clearAuthToken();
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth token management
  setAuthToken(token) {
    if (token) {
      localStorage.setItem('authToken', token);
      this.client.defaults.headers.Authorization = `Bearer ${token}`;
    }
  }

  clearAuthToken() {
    localStorage.removeItem('authToken');
    delete this.client.defaults.headers.Authorization;
  }

  getStoredToken() {
    return localStorage.getItem('authToken');
  }

  // Generic HTTP methods
  async get(url, config = {}) {
    const response = await this.client.get(url, config);
    return response.data;
  }

  async post(url, data = {}, config = {}) {
    const response = await this.client.post(url, data, config);
    return response.data;
  }

  async put(url, data = {}, config = {}) {
    const response = await this.client.put(url, data, config);
    return response.data;
  }

  async delete(url, config = {}) {
    const response = await this.client.delete(url, config);
    return response.data;
  }

  // Auth endpoints
  auth = {
    register: (data) => this.post('/auth/register', data),
    getProfile: () => this.get('/auth/me'),
    updateProfile: (data) => this.put('/auth/profile', data),
    deleteAccount: () => this.delete('/auth/account'),
  };

  // Movie endpoints
  movies = {
    search: (query, page = 1) => this.get(`/movies/search?q=${encodeURIComponent(query)}&page=${page}`),
    popular: (page = 1) => this.get(`/movies/popular?page=${page}`),
    trending: (timeWindow = 'week', page = 1) => this.get(`/movies/trending?time_window=${timeWindow}&page=${page}`),
    nowPlaying: (page = 1) => this.get(`/movies/now-playing?page=${page}`),
    upcoming: (page = 1) => this.get(`/movies/upcoming?page=${page}`),
    topRated: (page = 1) => this.get(`/movies/top-rated?page=${page}`),
    discover: (filters = {}, page = 1) => {
      const params = new URLSearchParams({ ...filters, page });
      return this.get(`/movies/discover?${params}`);
    },
    genres: () => this.get('/movies/genres'),
    getById: (id) => this.get(`/movies/${id}`),
  };

  // Review endpoints
  reviews = {
    create: (data) => this.post('/reviews', data),
    getAll: (params = {}) => {
      const queryString = new URLSearchParams(params).toString();
      return this.get(`/reviews?${queryString}`);
    },
    getById: (id) => this.get(`/reviews/${id}`),
    update: (id, data) => this.put(`/reviews/${id}`, data),
    delete: (id) => this.delete(`/reviews/${id}`),
    like: (id) => this.post(`/reviews/${id}/like`),
  };

  // List endpoints
  lists = {
    create: (data) => this.post('/lists', data),
    getAll: (params = {}) => {
      const queryString = new URLSearchParams(params).toString();
      return this.get(`/lists?${queryString}`);
    },
    getById: (id) => this.get(`/lists/${id}`),
    update: (id, data) => this.put(`/lists/${id}`, data),
    delete: (id) => this.delete(`/lists/${id}`),
    addMovie: (id, movieData) => this.post(`/lists/${id}/movies`, movieData),
    removeMovie: (id, movieId) => this.delete(`/lists/${id}/movies/${movieId}`),
    reorderMovies: (id, movieOrders) => this.put(`/lists/${id}/movies/reorder`, { movieOrders }),
    like: (id) => this.post(`/lists/${id}/like`),
    getWatchlist: () => this.get('/lists/watchlist'),
  };

  // User endpoints
  users = {
    search: (query, page = 1, limit = 20) => this.get(`/users/search?q=${encodeURIComponent(query)}&page=${page}&limit=${limit}`),
    getById: (id) => this.get(`/users/${id}`),
    getReviews: (id, params = {}) => {
      const queryString = new URLSearchParams(params).toString();
      return this.get(`/users/${id}/reviews?${queryString}`);
    },
    getLists: (id, params = {}) => {
      const queryString = new URLSearchParams(params).toString();
      return this.get(`/users/${id}/lists?${queryString}`);
    },
    getWatchlist: (id) => this.get(`/users/${id}/watchlist`),
  };

  // Social endpoints
  social = {
    follow: (userId) => this.post(`/social/follow/${userId}`),
    getFeed: (page = 1, limit = 20) => this.get(`/social/feed?page=${page}&limit=${limit}`),
    addReviewComment: (reviewId, text) => this.post(`/social/reviews/${reviewId}/comment`, { text }),
    deleteReviewComment: (reviewId, commentId) => this.delete(`/social/reviews/${reviewId}/comment/${commentId}`),
    addListComment: (listId, text) => this.post(`/social/lists/${listId}/comment`, { text }),
    deleteListComment: (listId, commentId) => this.delete(`/social/lists/${listId}/comment/${commentId}`),
    getFollowers: (userId, page = 1, limit = 20) => this.get(`/social/followers/${userId}?page=${page}&limit=${limit}`),
    getFollowing: (userId, page = 1, limit = 20) => this.get(`/social/following/${userId}?page=${page}&limit=${limit}`),
  };

  // Utility methods
  getImageUrl(path, size = 'w500') {
    if (!path) return null;
    return `https://image.tmdb.org/t/p/${size}${path}`;
  }

  formatDate(dateString) {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  formatRelativeTime(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
    
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
  }

  // Health check
  async healthCheck() {
    try {
      const response = await this.get('/health');
      return response;
    } catch (error) {
      console.error('Health check failed:', error);
      throw error;
    }
  }
}

export const api = new ApiService();
