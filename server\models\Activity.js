const mongoose = require('mongoose');

const activitySchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  type: {
    type: String,
    required: true,
    enum: [
      'review_created',
      'review_liked',
      'list_created',
      'list_liked',
      'movie_watched',
      'movie_rated',
      'user_followed',
      'list_updated'
    ],
    index: true
  },
  targetUser: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  review: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Review'
  },
  list: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'List'
  },
  movie: {
    tmdbId: Number,
    title: String,
    posterPath: String,
    releaseDate: String
  },
  metadata: {
    rating: Number,
    reviewText: String,
    listName: String
  },
  visibility: {
    type: String,
    enum: ['public', 'private', 'friends'],
    default: 'public'
  }
}, {
  timestamps: true
});

// Indexes for performance
activitySchema.index({ user: 1, createdAt: -1 });
activitySchema.index({ type: 1, createdAt: -1 });
activitySchema.index({ targetUser: 1, createdAt: -1 });
activitySchema.index({ visibility: 1, createdAt: -1 });

// TTL index to automatically delete old activities (optional - keep 6 months)
activitySchema.index({ createdAt: 1 }, { expireAfterSeconds: 15552000 }); // 6 months

module.exports = mongoose.model('Activity', activitySchema);
