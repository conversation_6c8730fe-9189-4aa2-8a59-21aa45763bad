const express = require('express');
const { query, validationResult } = require('express-validator');
const { optionalAuth } = require('../middleware/auth');
const tmdbService = require('../services/tmdbService');

const router = express.Router();

// @route   GET /api/movies/search
// @desc    Search movies by title
// @access  Public
router.get('/search',
  [
    query('q')
      .trim()
      .isLength({ min: 1 })
      .withMessage('Search query is required'),
    query('page')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Page must be between 1 and 1000')
  ],
  optionalAuth,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { q: query, page = 1 } = req.query;
      
      const results = await tmdbService.searchMovies(query, parseInt(page));
      
      // Format the results
      const formattedResults = {
        ...results,
        results: results.results.map(movie => tmdbService.formatMovieData(movie))
      };

      res.json(formattedResults);
    } catch (error) {
      console.error('Movie search error:', error);
      res.status(500).json({ message: 'Failed to search movies' });
    }
  }
);

// @route   GET /api/movies/popular
// @desc    Get popular movies
// @access  Public
router.get('/popular',
  [
    query('page')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Page must be between 1 and 1000')
  ],
  optionalAuth,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { page = 1 } = req.query;
      
      const results = await tmdbService.getPopularMovies(parseInt(page));
      
      const formattedResults = {
        ...results,
        results: results.results.map(movie => tmdbService.formatMovieData(movie))
      };

      res.json(formattedResults);
    } catch (error) {
      console.error('Popular movies error:', error);
      res.status(500).json({ message: 'Failed to fetch popular movies' });
    }
  }
);

// @route   GET /api/movies/trending
// @desc    Get trending movies
// @access  Public
router.get('/trending',
  [
    query('time_window')
      .optional()
      .isIn(['day', 'week'])
      .withMessage('Time window must be day or week'),
    query('page')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Page must be between 1 and 1000')
  ],
  optionalAuth,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { time_window = 'week', page = 1 } = req.query;
      
      const results = await tmdbService.getTrendingMovies(time_window, parseInt(page));
      
      const formattedResults = {
        ...results,
        results: results.results.map(movie => tmdbService.formatMovieData(movie))
      };

      res.json(formattedResults);
    } catch (error) {
      console.error('Trending movies error:', error);
      res.status(500).json({ message: 'Failed to fetch trending movies' });
    }
  }
);

// @route   GET /api/movies/now-playing
// @desc    Get now playing movies
// @access  Public
router.get('/now-playing',
  [
    query('page')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Page must be between 1 and 1000')
  ],
  optionalAuth,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { page = 1 } = req.query;
      
      const results = await tmdbService.getNowPlayingMovies(parseInt(page));
      
      const formattedResults = {
        ...results,
        results: results.results.map(movie => tmdbService.formatMovieData(movie))
      };

      res.json(formattedResults);
    } catch (error) {
      console.error('Now playing movies error:', error);
      res.status(500).json({ message: 'Failed to fetch now playing movies' });
    }
  }
);

// @route   GET /api/movies/upcoming
// @desc    Get upcoming movies
// @access  Public
router.get('/upcoming',
  [
    query('page')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Page must be between 1 and 1000')
  ],
  optionalAuth,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { page = 1 } = req.query;
      
      const results = await tmdbService.getUpcomingMovies(parseInt(page));
      
      const formattedResults = {
        ...results,
        results: results.results.map(movie => tmdbService.formatMovieData(movie))
      };

      res.json(formattedResults);
    } catch (error) {
      console.error('Upcoming movies error:', error);
      res.status(500).json({ message: 'Failed to fetch upcoming movies' });
    }
  }
);

// @route   GET /api/movies/top-rated
// @desc    Get top rated movies
// @access  Public
router.get('/top-rated',
  [
    query('page')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Page must be between 1 and 1000')
  ],
  optionalAuth,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { page = 1 } = req.query;
      
      const results = await tmdbService.getTopRatedMovies(parseInt(page));
      
      const formattedResults = {
        ...results,
        results: results.results.map(movie => tmdbService.formatMovieData(movie))
      };

      res.json(formattedResults);
    } catch (error) {
      console.error('Top rated movies error:', error);
      res.status(500).json({ message: 'Failed to fetch top rated movies' });
    }
  }
);

// @route   GET /api/movies/discover
// @desc    Discover movies with filters
// @access  Public
router.get('/discover',
  [
    query('page')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Page must be between 1 and 1000'),
    query('with_genres')
      .optional()
      .matches(/^\d+(,\d+)*$/)
      .withMessage('Genres must be comma-separated numbers'),
    query('primary_release_year')
      .optional()
      .isInt({ min: 1900, max: new Date().getFullYear() + 5 })
      .withMessage('Release year must be valid'),
    query('sort_by')
      .optional()
      .isIn(['popularity.desc', 'popularity.asc', 'release_date.desc', 'release_date.asc', 'vote_average.desc', 'vote_average.asc'])
      .withMessage('Invalid sort option')
  ],
  optionalAuth,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { page = 1, ...filters } = req.query;
      
      const results = await tmdbService.discoverMovies(filters, parseInt(page));
      
      const formattedResults = {
        ...results,
        results: results.results.map(movie => tmdbService.formatMovieData(movie))
      };

      res.json(formattedResults);
    } catch (error) {
      console.error('Discover movies error:', error);
      res.status(500).json({ message: 'Failed to discover movies' });
    }
  }
);

// @route   GET /api/movies/genres
// @desc    Get movie genres
// @access  Public
router.get('/genres', async (req, res) => {
  try {
    const genres = await tmdbService.getGenres();
    res.json({ genres });
  } catch (error) {
    console.error('Genres error:', error);
    res.status(500).json({ message: 'Failed to fetch genres' });
  }
});

// @route   GET /api/movies/:id
// @desc    Get movie details by ID
// @access  Public
router.get('/:id',
  optionalAuth,
  async (req, res) => {
    try {
      const { id } = req.params;
      
      if (!id || isNaN(id)) {
        return res.status(400).json({ message: 'Valid movie ID is required' });
      }

      const movie = await tmdbService.getMovieDetails(parseInt(id));
      const formattedMovie = tmdbService.formatMovieData(movie);

      res.json(formattedMovie);
    } catch (error) {
      console.error('Movie details error:', error);
      if (error.response && error.response.status === 404) {
        return res.status(404).json({ message: 'Movie not found' });
      }
      res.status(500).json({ message: 'Failed to fetch movie details' });
    }
  }
);

module.exports = router;
