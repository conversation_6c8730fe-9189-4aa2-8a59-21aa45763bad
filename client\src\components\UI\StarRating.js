import React, { useState } from 'react';
import { FaStar, FaStarHalfAlt } from 'react-icons/fa';

const StarRating = ({ 
  rating = 0, 
  onRatingChange, 
  readonly = false, 
  size = 'md',
  showValue = false,
  className = ''
}) => {
  const [hoverRating, setHoverRating] = useState(0);

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
    xl: 'w-8 h-8'
  };

  const handleClick = (value) => {
    if (!readonly && onRatingChange) {
      onRatingChange(value);
    }
  };

  const handleMouseEnter = (value) => {
    if (!readonly) {
      setHoverRating(value);
    }
  };

  const handleMouseLeave = () => {
    if (!readonly) {
      setHoverRating(0);
    }
  };

  const renderStar = (index) => {
    const value = index + 0.5;
    const fullValue = index + 1;
    const currentRating = hoverRating || rating;
    
    const isHalfFilled = currentRating >= value && currentRating < fullValue;
    const isFilled = currentRating >= fullValue;

    return (
      <div key={index} className="relative">
        {/* Half star button */}
        <button
          type="button"
          className={`absolute left-0 top-0 w-1/2 h-full z-10 ${
            readonly ? 'cursor-default' : 'cursor-pointer'
          }`}
          onClick={() => handleClick(value)}
          onMouseEnter={() => handleMouseEnter(value)}
          onMouseLeave={handleMouseLeave}
          disabled={readonly}
          aria-label={`Rate ${value} stars`}
        />
        
        {/* Full star button */}
        <button
          type="button"
          className={`absolute right-0 top-0 w-1/2 h-full z-10 ${
            readonly ? 'cursor-default' : 'cursor-pointer'
          }`}
          onClick={() => handleClick(fullValue)}
          onMouseEnter={() => handleMouseEnter(fullValue)}
          onMouseLeave={handleMouseLeave}
          disabled={readonly}
          aria-label={`Rate ${fullValue} stars`}
        />

        {/* Star icon */}
        <div className="relative">
          {isHalfFilled ? (
            <FaStarHalfAlt 
              className={`${sizeClasses[size]} text-yellow-400 transition-colors duration-150`}
            />
          ) : (
            <FaStar 
              className={`${sizeClasses[size]} transition-colors duration-150 ${
                isFilled ? 'text-yellow-400' : 'text-gray-600'
              }`}
            />
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      <div className="flex items-center space-x-0.5">
        {[0, 1, 2, 3, 4].map(renderStar)}
      </div>
      
      {showValue && (
        <span className="text-sm text-gray-400 ml-2">
          {rating > 0 ? rating.toFixed(1) : '—'}
        </span>
      )}
    </div>
  );
};

export default StarRating;
