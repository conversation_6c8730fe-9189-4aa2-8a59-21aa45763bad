@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-dark-900 text-white antialiased;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-dark-800;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-600 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500;
  }
}

@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-dark-900;
  }
  
  .btn-primary {
    @apply btn bg-primary-500 text-dark-900 hover:bg-primary-400 focus:ring-primary-500 shadow-glow hover:shadow-glow-lg;
  }
  
  .btn-secondary {
    @apply btn bg-dark-700 text-white hover:bg-dark-600 focus:ring-dark-500 border border-dark-600;
  }
  
  .btn-ghost {
    @apply btn text-gray-300 hover:text-white hover:bg-dark-800 focus:ring-dark-500;
  }
  
  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-500 focus:ring-red-500;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  /* Input styles */
  .input {
    @apply w-full px-3 py-2 bg-dark-800 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
  }
  
  .input-error {
    @apply border-red-500 focus:ring-red-500;
  }
  
  /* Card styles */
  .card {
    @apply bg-dark-800 rounded-lg border border-dark-700 shadow-dark;
  }
  
  .card-hover {
    @apply card hover:border-dark-600 hover:shadow-dark-lg transition-all duration-200;
  }
  
  /* Movie poster styles */
  .movie-poster {
    @apply aspect-[2/3] bg-dark-700 rounded-lg overflow-hidden shadow-dark;
  }
  
  .movie-poster img {
    @apply w-full h-full object-cover transition-transform duration-300;
  }
  
  .movie-poster:hover img {
    @apply scale-105;
  }
  
  /* Star rating styles */
  .star-rating {
    @apply flex items-center space-x-1;
  }
  
  .star {
    @apply w-4 h-4 cursor-pointer transition-colors duration-150;
  }
  
  .star-filled {
    @apply text-yellow-400;
  }
  
  .star-empty {
    @apply text-gray-600;
  }
  
  .star-half {
    @apply text-yellow-400;
  }
  
  /* Loading skeleton */
  .skeleton {
    @apply animate-pulse bg-dark-700 rounded;
  }
  
  /* Text styles */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent;
  }
  
  /* Navigation styles */
  .nav-link {
    @apply px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-dark-800 rounded-lg transition-all duration-200;
  }
  
  .nav-link-active {
    @apply nav-link text-primary-500 bg-dark-800;
  }
  
  /* Modal styles */
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 flex items-center justify-center p-4;
  }
  
  .modal-content {
    @apply bg-dark-800 rounded-lg border border-dark-700 shadow-dark-lg max-w-lg w-full max-h-[90vh] overflow-y-auto;
  }
  
  /* Dropdown styles */
  .dropdown {
    @apply absolute right-0 mt-2 w-48 bg-dark-800 border border-dark-700 rounded-lg shadow-dark-lg z-10;
  }
  
  .dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-300 hover:text-white hover:bg-dark-700 transition-colors duration-150;
  }
  
  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-primary-500 text-dark-900;
  }
  
  .badge-secondary {
    @apply badge bg-dark-700 text-gray-300;
  }
  
  /* Avatar styles */
  .avatar {
    @apply rounded-full bg-dark-700 flex items-center justify-center overflow-hidden;
  }
  
  .avatar-sm {
    @apply avatar w-8 h-8;
  }
  
  .avatar-md {
    @apply avatar w-10 h-10;
  }
  
  .avatar-lg {
    @apply avatar w-12 h-12;
  }
  
  .avatar-xl {
    @apply avatar w-16 h-16;
  }
}

@layer utilities {
  /* Custom utilities */
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }
  
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }
  
  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Gradient text */
  .gradient-text {
    background: linear-gradient(135deg, #00FF85 0%, #4ade80 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}
