import React, { useState, useRef, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { FaSearch, FaUser, FaCog, FaSignOutAlt, FaFilm, FaHome, FaList, FaStar, FaUsers } from 'react-icons/fa';
// Authentication removed
import SearchBar from '../Search/SearchBar';

const Navigation = () => {
  const location = useLocation();
  const [showMobileMenu, setShowMobileMenu] = useState(false);

  // No authentication needed

  const isActive = (path) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  const navLinks = [
    { path: '/', label: 'Home', icon: FaHome },
    { path: '/movies', label: 'Films', icon: FaFilm },
    { path: '/reviews', label: 'Reviews', icon: FaStar },
    { path: '/lists', label: 'Lists', icon: FaList },
    { path: '/users', label: 'Members', icon: FaUsers },
  ];

  const userNavLinks = navLinks;

  return (
    <nav className="bg-dark-900 border-b border-dark-700 sticky top-0 z-40">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link 
            to="/" 
            className="flex items-center space-x-2 text-xl font-bold text-primary-500 hover:text-primary-400 transition-colors duration-200"
          >
            <FaFilm className="w-6 h-6" />
            <span className="hidden sm:block">Letterboxd</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            {userNavLinks.map(({ path, label, icon: Icon }) => (
              <Link
                key={path}
                to={path}
                className={`nav-link ${isActive(path) ? 'nav-link-active' : ''}`}
              >
                <Icon className="w-4 h-4 mr-2" />
                {label}
              </Link>
            ))}
          </div>

          {/* Search Bar */}
          <div className="flex-1 max-w-md mx-4">
            <SearchBar />
          </div>

          {/* No authentication - just show app info */}
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-400">
              Movie Discovery App
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setShowMobileMenu(!showMobileMenu)}
              className="md:hidden p-2 rounded-lg hover:bg-dark-800 transition-colors duration-200"
            >
              <div className="w-5 h-5 flex flex-col justify-center space-y-1">
                <div className="w-full h-0.5 bg-current"></div>
                <div className="w-full h-0.5 bg-current"></div>
                <div className="w-full h-0.5 bg-current"></div>
              </div>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {showMobileMenu && (
          <div className="md:hidden py-4 border-t border-dark-700">
            <div className="flex flex-col space-y-2">
              {userNavLinks.map(({ path, label, icon: Icon }) => (
                <Link
                  key={path}
                  to={path}
                  className={`nav-link ${isActive(path) ? 'nav-link-active' : ''}`}
                  onClick={() => setShowMobileMenu(false)}
                >
                  <Icon className="w-4 h-4 mr-2" />
                  {label}
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navigation;
