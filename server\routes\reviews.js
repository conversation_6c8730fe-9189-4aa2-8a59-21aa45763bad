const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { authenticateToken, optionalAuth } = require('../middleware/auth');
const Review = require('../models/Review');
const User = require('../models/User');
const Activity = require('../models/Activity');
const tmdbService = require('../services/tmdbService');

const router = express.Router();

// @route   POST /api/reviews
// @desc    Create a new review
// @access  Private
router.post('/',
  authenticateToken,
  [
    body('tmdbId')
      .isInt({ min: 1 })
      .withMessage('Valid TMDB ID is required'),
    body('movieTitle')
      .trim()
      .isLength({ min: 1, max: 200 })
      .withMessage('Movie title is required and must be less than 200 characters'),
    body('rating')
      .optional()
      .isFloat({ min: 0.5, max: 5 })
      .custom(value => value % 0.5 === 0)
      .withMessage('Rating must be between 0.5 and 5 in half-star increments'),
    body('reviewText')
      .optional()
      .trim()
      .isLength({ max: 2000 })
      .withMessage('Review text must be less than 2000 characters'),
    body('containsSpoilers')
      .optional()
      .isBoolean()
      .withMessage('Contains spoilers must be a boolean'),
    body('watchedDate')
      .optional()
      .isISO8601()
      .withMessage('Watched date must be a valid date'),
    body('isRewatch')
      .optional()
      .isBoolean()
      .withMessage('Is rewatch must be a boolean'),
    body('visibility')
      .optional()
      .isIn(['public', 'private', 'friends'])
      .withMessage('Visibility must be public, private, or friends'),
    body('tags')
      .optional()
      .isArray({ max: 10 })
      .withMessage('Maximum 10 tags allowed')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const {
        tmdbId,
        movieTitle,
        moviePosterPath,
        movieReleaseDate,
        rating,
        reviewText,
        containsSpoilers = false,
        watchedDate,
        isRewatch = false,
        visibility = 'public',
        tags = []
      } = req.body;

      // Check if user already has a review for this movie
      const existingReview = await Review.findOne({
        user: req.user._id,
        tmdbId
      });

      if (existingReview) {
        return res.status(400).json({ message: 'You have already reviewed this movie' });
      }

      // Create new review
      const review = new Review({
        user: req.user._id,
        tmdbId,
        movieTitle,
        moviePosterPath,
        movieReleaseDate,
        rating,
        reviewText,
        containsSpoilers,
        watchedDate: watchedDate || new Date(),
        isRewatch,
        visibility,
        tags: tags.map(tag => tag.toLowerCase().trim())
      });

      await review.save();

      // Update user stats
      await User.findByIdAndUpdate(req.user._id, {
        $inc: { 'stats.totalReviews': 1 },
        $addToSet: { 'watchedFilms': { tmdbId, watchedAt: review.watchedDate } }
      });

      // Create activity
      if (visibility === 'public') {
        await Activity.create({
          user: req.user._id,
          type: 'review_created',
          review: review._id,
          movie: {
            tmdbId,
            title: movieTitle,
            posterPath: moviePosterPath,
            releaseDate: movieReleaseDate
          },
          metadata: {
            rating,
            reviewText: reviewText ? reviewText.substring(0, 100) : null
          }
        });
      }

      // Populate user data for response
      await review.populate('user', 'displayName username photoURL');

      res.status(201).json({
        message: 'Review created successfully',
        review
      });
    } catch (error) {
      console.error('Create review error:', error);
      res.status(500).json({ message: 'Server error creating review' });
    }
  }
);

// @route   GET /api/reviews
// @desc    Get reviews with filters
// @access  Public
router.get('/',
  [
    query('page')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Page must be between 1 and 1000'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 50 })
      .withMessage('Limit must be between 1 and 50'),
    query('user')
      .optional()
      .isMongoId()
      .withMessage('Valid user ID required'),
    query('movie')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Valid movie ID required'),
    query('rating')
      .optional()
      .isFloat({ min: 0.5, max: 5 })
      .withMessage('Rating must be between 0.5 and 5'),
    query('sort')
      .optional()
      .isIn(['newest', 'oldest', 'rating_high', 'rating_low', 'most_liked'])
      .withMessage('Invalid sort option')
  ],
  optionalAuth,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const {
        page = 1,
        limit = 20,
        user: userId,
        movie: tmdbId,
        rating,
        sort = 'newest'
      } = req.query;

      // Build query
      const query = { visibility: 'public' };
      
      if (userId) query.user = userId;
      if (tmdbId) query.tmdbId = parseInt(tmdbId);
      if (rating) query.rating = parseFloat(rating);

      // Build sort
      let sortQuery = {};
      switch (sort) {
        case 'oldest':
          sortQuery = { createdAt: 1 };
          break;
        case 'rating_high':
          sortQuery = { rating: -1, createdAt: -1 };
          break;
        case 'rating_low':
          sortQuery = { rating: 1, createdAt: -1 };
          break;
        case 'most_liked':
          sortQuery = { 'stats.likesCount': -1, createdAt: -1 };
          break;
        default:
          sortQuery = { createdAt: -1 };
      }

      const skip = (parseInt(page) - 1) * parseInt(limit);

      const [reviews, total] = await Promise.all([
        Review.find(query)
          .populate('user', 'displayName username photoURL')
          .sort(sortQuery)
          .skip(skip)
          .limit(parseInt(limit)),
        Review.countDocuments(query)
      ]);

      res.json({
        reviews,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });
    } catch (error) {
      console.error('Get reviews error:', error);
      res.status(500).json({ message: 'Server error fetching reviews' });
    }
  }
);

// @route   GET /api/reviews/:id
// @desc    Get review by ID
// @access  Public
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const review = await Review.findById(req.params.id)
      .populate('user', 'displayName username photoURL')
      .populate('comments.user', 'displayName username photoURL');

    if (!review) {
      return res.status(404).json({ message: 'Review not found' });
    }

    // Check if review is private and user is not the owner
    if (review.visibility === 'private' && (!req.user || review.user._id.toString() !== req.user._id.toString())) {
      return res.status(403).json({ message: 'Access denied' });
    }

    res.json(review);
  } catch (error) {
    console.error('Get review error:', error);
    res.status(500).json({ message: 'Server error fetching review' });
  }
});

// @route   PUT /api/reviews/:id
// @desc    Update review
// @access  Private (owner only)
router.put('/:id',
  authenticateToken,
  [
    body('rating')
      .optional()
      .isFloat({ min: 0.5, max: 5 })
      .custom(value => value % 0.5 === 0)
      .withMessage('Rating must be between 0.5 and 5 in half-star increments'),
    body('reviewText')
      .optional()
      .trim()
      .isLength({ max: 2000 })
      .withMessage('Review text must be less than 2000 characters'),
    body('containsSpoilers')
      .optional()
      .isBoolean()
      .withMessage('Contains spoilers must be a boolean'),
    body('visibility')
      .optional()
      .isIn(['public', 'private', 'friends'])
      .withMessage('Visibility must be public, private, or friends'),
    body('tags')
      .optional()
      .isArray({ max: 10 })
      .withMessage('Maximum 10 tags allowed')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const review = await Review.findById(req.params.id);

      if (!review) {
        return res.status(404).json({ message: 'Review not found' });
      }

      // Check if user owns the review
      if (review.user.toString() !== req.user._id.toString()) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const { rating, reviewText, containsSpoilers, visibility, tags } = req.body;

      // Update fields if provided
      if (rating !== undefined) review.rating = rating;
      if (reviewText !== undefined) review.reviewText = reviewText;
      if (containsSpoilers !== undefined) review.containsSpoilers = containsSpoilers;
      if (visibility !== undefined) review.visibility = visibility;
      if (tags !== undefined) review.tags = tags.map(tag => tag.toLowerCase().trim());

      await review.save();
      await review.populate('user', 'displayName username photoURL');

      res.json({
        message: 'Review updated successfully',
        review
      });
    } catch (error) {
      console.error('Update review error:', error);
      res.status(500).json({ message: 'Server error updating review' });
    }
  }
);

// @route   DELETE /api/reviews/:id
// @desc    Delete review
// @access  Private (owner only)
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const review = await Review.findById(req.params.id);

    if (!review) {
      return res.status(404).json({ message: 'Review not found' });
    }

    // Check if user owns the review
    if (review.user.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    await Review.findByIdAndDelete(req.params.id);

    // Update user stats
    await User.findByIdAndUpdate(req.user._id, {
      $inc: { 'stats.totalReviews': -1 }
    });

    // Delete related activities
    await Activity.deleteMany({ review: req.params.id });

    res.json({ message: 'Review deleted successfully' });
  } catch (error) {
    console.error('Delete review error:', error);
    res.status(500).json({ message: 'Server error deleting review' });
  }
});

// @route   POST /api/reviews/:id/like
// @desc    Like/unlike a review
// @access  Private
router.post('/:id/like', authenticateToken, async (req, res) => {
  try {
    const review = await Review.findById(req.params.id);

    if (!review) {
      return res.status(404).json({ message: 'Review not found' });
    }

    const isLiked = review.isLikedBy(req.user._id);

    if (isLiked) {
      review.removeLike(req.user._id);
    } else {
      review.addLike(req.user._id);

      // Create activity if liking someone else's review
      if (review.user.toString() !== req.user._id.toString()) {
        await Activity.create({
          user: req.user._id,
          type: 'review_liked',
          targetUser: review.user,
          review: review._id,
          movie: {
            tmdbId: review.tmdbId,
            title: review.movieTitle,
            posterPath: review.moviePosterPath,
            releaseDate: review.movieReleaseDate
          }
        });
      }
    }

    await review.save();

    res.json({
      message: isLiked ? 'Review unliked' : 'Review liked',
      isLiked: !isLiked,
      likesCount: review.likes.length
    });
  } catch (error) {
    console.error('Like review error:', error);
    res.status(500).json({ message: 'Server error liking review' });
  }
});

module.exports = router;
