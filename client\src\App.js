import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from './context/AuthContext';
import { useAuth } from './context/AuthContext';

// Layout components
import Layout from './components/Layout/Layout';
import LoadingSpinner from './components/UI/LoadingSpinner';

// Page components
import HomePage from './pages/HomePage';
import LoginPage from './pages/LoginPage';
import MoviesPage from './pages/MoviesPage';
import MovieDetailPage from './pages/MovieDetailPage';
import ReviewsPage from './pages/ReviewsPage';
import ReviewDetailPage from './pages/ReviewDetailPage';
import ListsPage from './pages/ListsPage';
import ListDetailPage from './pages/ListDetailPage';
import ProfilePage from './pages/ProfilePage';
import SettingsPage from './pages/SettingsPage';
import SearchPage from './pages/SearchPage';
import FeedPage from './pages/FeedPage';
import NotFoundPage from './pages/NotFoundPage';

// Protected Route component
const ProtectedRoute = ({ children }) => {
  const { currentUser, loading } = useAuth();
  
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }
  
  return currentUser ? children : <Navigate to="/login" />;
};

// Public Route component (redirect to home if authenticated)
const PublicRoute = ({ children }) => {
  const { currentUser, loading } = useAuth();
  
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }
  
  return currentUser ? <Navigate to="/" /> : children;
};

// App Routes component
const AppRoutes = () => {
  return (
    <Routes>
      {/* Public routes */}
      <Route path="/" element={<Layout><HomePage /></Layout>} />
      <Route path="/movies" element={<Layout><MoviesPage /></Layout>} />
      <Route path="/movies/:id" element={<Layout><MovieDetailPage /></Layout>} />
      <Route path="/reviews" element={<Layout><ReviewsPage /></Layout>} />
      <Route path="/reviews/:id" element={<Layout><ReviewDetailPage /></Layout>} />
      <Route path="/lists" element={<Layout><ListsPage /></Layout>} />
      <Route path="/lists/:id" element={<Layout><ListDetailPage /></Layout>} />
      <Route path="/users/:id" element={<Layout><ProfilePage /></Layout>} />
      <Route path="/search" element={<Layout><SearchPage /></Layout>} />
      
      {/* Auth routes */}
      <Route 
        path="/login" 
        element={
          <PublicRoute>
            <LoginPage />
          </PublicRoute>
        } 
      />
      
      {/* Protected routes */}
      <Route 
        path="/feed" 
        element={
          <ProtectedRoute>
            <Layout><FeedPage /></Layout>
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/profile" 
        element={
          <ProtectedRoute>
            <Layout><ProfilePage /></Layout>
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/settings" 
        element={
          <ProtectedRoute>
            <Layout><SettingsPage /></Layout>
          </ProtectedRoute>
        } 
      />
      
      {/* 404 route */}
      <Route path="*" element={<Layout><NotFoundPage /></Layout>} />
    </Routes>
  );
};

// Main App component
function App() {
  return (
    <div className="App">
      <AuthProvider>
        <Router>
          <AppRoutes />
          <Toaster
            position="bottom-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#1f2937',
                color: '#ffffff',
                border: '1px solid #374151',
              },
              success: {
                iconTheme: {
                  primary: '#00FF85',
                  secondary: '#1f2937',
                },
              },
              error: {
                iconTheme: {
                  primary: '#ef4444',
                  secondary: '#1f2937',
                },
              },
            }}
          />
        </Router>
      </AuthProvider>
    </div>
  );
}

export default App;
