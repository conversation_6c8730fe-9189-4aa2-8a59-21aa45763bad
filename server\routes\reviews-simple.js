const express = require('express');
const { authenticateToken, optionalAuth } = require('../middleware/auth');
const firestoreService = require('../services/firestoreService');

const router = express.Router();

// @route   POST /api/reviews
// @desc    Create a new review (simplified)
// @access  Private
router.post('/', authenticateToken, async (req, res) => {
  try {
    res.json({ message: 'Review creation endpoint - coming soon' });
  } catch (error) {
    console.error('Create review error:', error);
    res.status(500).json({ message: 'Server error creating review' });
  }
});

// @route   GET /api/reviews
// @desc    Get all reviews (simplified)
// @access  Public
router.get('/', optionalAuth, async (req, res) => {
  try {
    const reviews = await firestoreService.query('reviews', [
      ['visibility', '==', 'public']
    ], ['createdAt', 'desc'], 20);

    res.json({
      reviews: reviews || [],
      pagination: {
        page: 1,
        limit: 20,
        total: reviews?.length || 0,
        pages: 1
      }
    });
  } catch (error) {
    console.error('Get reviews error:', error);
    res.status(500).json({ message: 'Server error fetching reviews' });
  }
});

// @route   GET /api/reviews/:id
// @desc    Get review by ID (simplified)
// @access  Public
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const review = await firestoreService.getById('reviews', req.params.id);
    
    if (!review) {
      return res.status(404).json({ message: 'Review not found' });
    }

    res.json(review);
  } catch (error) {
    console.error('Get review error:', error);
    res.status(500).json({ message: 'Server error fetching review' });
  }
});

module.exports = router;
