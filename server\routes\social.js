const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { authenticateToken } = require('../middleware/auth');
const User = require('../models/User');
const Activity = require('../models/Activity');
const Review = require('../models/Review');
const List = require('../models/List');

const router = express.Router();

// @route   POST /api/social/follow/:userId
// @desc    Follow/unfollow a user
// @access  Private
router.post('/follow/:userId', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;
    const currentUserId = req.user._id;

    if (userId === currentUserId.toString()) {
      return res.status(400).json({ message: 'Cannot follow yourself' });
    }

    const [userToFollow, currentUser] = await Promise.all([
      User.findById(userId),
      User.findById(currentUserId)
    ]);

    if (!userToFollow) {
      return res.status(404).json({ message: 'User not found' });
    }

    const isFollowing = currentUser.following.includes(userId);

    if (isFollowing) {
      // Unfollow
      currentUser.following.pull(userId);
      userToFollow.followers.pull(currentUserId);
    } else {
      // Follow
      currentUser.following.push(userId);
      userToFollow.followers.push(currentUserId);

      // Create activity
      await Activity.create({
        user: currentUserId,
        type: 'user_followed',
        targetUser: userId
      });
    }

    await Promise.all([
      currentUser.save(),
      userToFollow.save()
    ]);

    res.json({
      message: isFollowing ? 'User unfollowed' : 'User followed',
      isFollowing: !isFollowing,
      followersCount: userToFollow.followers.length
    });
  } catch (error) {
    console.error('Follow user error:', error);
    res.status(500).json({ message: 'Server error following user' });
  }
});

// @route   GET /api/social/feed
// @desc    Get activity feed for current user
// @access  Private
router.get('/feed',
  [
    query('page')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Page must be between 1 and 1000'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 50 })
      .withMessage('Limit must be between 1 and 50')
  ],
  authenticateToken,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { page = 1, limit = 20 } = req.query;
      const skip = (parseInt(page) - 1) * parseInt(limit);

      // Get users that current user is following
      const currentUser = await User.findById(req.user._id).select('following');
      const followingIds = currentUser.following;

      // Include current user's activities too
      followingIds.push(req.user._id);

      const [activities, total] = await Promise.all([
        Activity.find({
          user: { $in: followingIds },
          visibility: 'public'
        })
        .populate('user', 'displayName username photoURL')
        .populate('targetUser', 'displayName username photoURL')
        .populate('review', 'rating reviewText movieTitle moviePosterPath')
        .populate('list', 'name description movies')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit)),
        Activity.countDocuments({
          user: { $in: followingIds },
          visibility: 'public'
        })
      ]);

      res.json({
        activities,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });
    } catch (error) {
      console.error('Get feed error:', error);
      res.status(500).json({ message: 'Server error fetching feed' });
    }
  }
);

// @route   POST /api/social/reviews/:reviewId/comment
// @desc    Add comment to a review
// @access  Private
router.post('/reviews/:reviewId/comment',
  authenticateToken,
  [
    body('text')
      .trim()
      .isLength({ min: 1, max: 500 })
      .withMessage('Comment text is required and must be less than 500 characters')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { text } = req.body;
      const review = await Review.findById(req.params.reviewId);

      if (!review) {
        return res.status(404).json({ message: 'Review not found' });
      }

      // Check if review is public or user has access
      if (review.visibility === 'private') {
        return res.status(403).json({ message: 'Cannot comment on private review' });
      }

      const comment = {
        user: req.user._id,
        text,
        createdAt: new Date()
      };

      review.comments.push(comment);
      await review.save();

      // Populate the new comment
      await review.populate('comments.user', 'displayName username photoURL');
      const newComment = review.comments[review.comments.length - 1];

      res.status(201).json({
        message: 'Comment added successfully',
        comment: newComment
      });
    } catch (error) {
      console.error('Add comment error:', error);
      res.status(500).json({ message: 'Server error adding comment' });
    }
  }
);

// @route   DELETE /api/social/reviews/:reviewId/comment/:commentId
// @desc    Delete comment from review
// @access  Private (comment owner or review owner)
router.delete('/reviews/:reviewId/comment/:commentId', authenticateToken, async (req, res) => {
  try {
    const review = await Review.findById(req.params.reviewId);

    if (!review) {
      return res.status(404).json({ message: 'Review not found' });
    }

    const comment = review.comments.id(req.params.commentId);

    if (!comment) {
      return res.status(404).json({ message: 'Comment not found' });
    }

    // Check if user owns the comment or the review
    if (comment.user.toString() !== req.user._id.toString() && 
        review.user.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    comment.remove();
    await review.save();

    res.json({ message: 'Comment deleted successfully' });
  } catch (error) {
    console.error('Delete comment error:', error);
    res.status(500).json({ message: 'Server error deleting comment' });
  }
});

// @route   POST /api/social/lists/:listId/comment
// @desc    Add comment to a list
// @access  Private
router.post('/lists/:listId/comment',
  authenticateToken,
  [
    body('text')
      .trim()
      .isLength({ min: 1, max: 500 })
      .withMessage('Comment text is required and must be less than 500 characters')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { text } = req.body;
      const list = await List.findById(req.params.listId);

      if (!list) {
        return res.status(404).json({ message: 'List not found' });
      }

      // Check if list is public and not a watchlist
      if (list.visibility === 'private' || list.isWatchlist) {
        return res.status(403).json({ message: 'Cannot comment on this list' });
      }

      const comment = {
        user: req.user._id,
        text,
        createdAt: new Date()
      };

      list.comments.push(comment);
      await list.save();

      // Populate the new comment
      await list.populate('comments.user', 'displayName username photoURL');
      const newComment = list.comments[list.comments.length - 1];

      res.status(201).json({
        message: 'Comment added successfully',
        comment: newComment
      });
    } catch (error) {
      console.error('Add list comment error:', error);
      res.status(500).json({ message: 'Server error adding comment' });
    }
  }
);

// @route   DELETE /api/social/lists/:listId/comment/:commentId
// @desc    Delete comment from list
// @access  Private (comment owner or list owner)
router.delete('/lists/:listId/comment/:commentId', authenticateToken, async (req, res) => {
  try {
    const list = await List.findById(req.params.listId);

    if (!list) {
      return res.status(404).json({ message: 'List not found' });
    }

    const comment = list.comments.id(req.params.commentId);

    if (!comment) {
      return res.status(404).json({ message: 'Comment not found' });
    }

    // Check if user owns the comment or the list
    if (comment.user.toString() !== req.user._id.toString() && 
        list.user.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    comment.remove();
    await list.save();

    res.json({ message: 'Comment deleted successfully' });
  } catch (error) {
    console.error('Delete list comment error:', error);
    res.status(500).json({ message: 'Server error deleting comment' });
  }
});

// @route   GET /api/social/followers/:userId
// @desc    Get user's followers
// @access  Public
router.get('/followers/:userId',
  [
    query('page')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Page must be between 1 and 1000'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 50 })
      .withMessage('Limit must be between 1 and 50')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { page = 1, limit = 20 } = req.query;
      const skip = (parseInt(page) - 1) * parseInt(limit);

      const user = await User.findById(req.params.userId)
        .populate({
          path: 'followers',
          select: 'displayName username photoURL bio stats',
          options: {
            skip,
            limit: parseInt(limit)
          }
        });

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      const total = user.followers.length;

      res.json({
        followers: user.followers,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });
    } catch (error) {
      console.error('Get followers error:', error);
      res.status(500).json({ message: 'Server error fetching followers' });
    }
  }
);

// @route   GET /api/social/following/:userId
// @desc    Get users that a user is following
// @access  Public
router.get('/following/:userId',
  [
    query('page')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Page must be between 1 and 1000'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 50 })
      .withMessage('Limit must be between 1 and 50')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { page = 1, limit = 20 } = req.query;
      const skip = (parseInt(page) - 1) * parseInt(limit);

      const user = await User.findById(req.params.userId)
        .populate({
          path: 'following',
          select: 'displayName username photoURL bio stats',
          options: {
            skip,
            limit: parseInt(limit)
          }
        });

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      const total = user.following.length;

      res.json({
        following: user.following,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });
    } catch (error) {
      console.error('Get following error:', error);
      res.status(500).json({ message: 'Server error fetching following' });
    }
  }
);

module.exports = router;
