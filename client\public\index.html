<!DOCTYPE html>
<html lang="en" class="dark">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#14181C" />
    <meta name="description" content="A social platform for film enthusiasts to discover, rate, and review movies" />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://image.tmdb.org">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Crimson+Text:ital,wght@0,400;0,600;1,400&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    
    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    
    <!-- Manifest -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://letterboxd-clone.vercel.app/" />
    <meta property="og:title" content="Letterboxd Clone - Social Film Discovery" />
    <meta property="og:description" content="Discover, rate, and review movies with fellow film enthusiasts" />
    <meta property="og:image" content="%PUBLIC_URL%/og-image.png" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://letterboxd-clone.vercel.app/" />
    <meta property="twitter:title" content="Letterboxd Clone - Social Film Discovery" />
    <meta property="twitter:description" content="Discover, rate, and review movies with fellow film enthusiasts" />
    <meta property="twitter:image" content="%PUBLIC_URL%/og-image.png" />
    
    <title>Letterboxd Clone - Social Film Discovery</title>
    
    <style>
      /* Loading screen styles */
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #14181C;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.3s ease-out;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #374151;
        border-top: 3px solid #00FF85;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Hide loading screen when React loads */
      .loaded .loading-screen {
        opacity: 0;
        pointer-events: none;
      }
      
      /* Prevent flash of unstyled content */
      body {
        background-color: #14181C;
        color: #ffffff;
        font-family: 'Inter', system-ui, sans-serif;
      }
      
      /* Scrollbar styling */
      ::-webkit-scrollbar {
        width: 8px;
      }
      
      ::-webkit-scrollbar-track {
        background: #1f2937;
      }
      
      ::-webkit-scrollbar-thumb {
        background: #4b5563;
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: #6b7280;
      }
    </style>
  </head>
  <body class="bg-dark-900 text-white antialiased">
    <noscript>You need to enable JavaScript to run this app.</noscript>
    
    <!-- Loading screen -->
    <div class="loading-screen">
      <div class="loading-spinner"></div>
    </div>
    
    <div id="root"></div>
    
    <script>
      // Hide loading screen when React loads
      window.addEventListener('load', function() {
        document.body.classList.add('loaded');
        setTimeout(function() {
          const loadingScreen = document.querySelector('.loading-screen');
          if (loadingScreen) {
            loadingScreen.remove();
          }
        }, 300);
      });
    </script>
  </body>
</html>
