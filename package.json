{"name": "letterboxd-clone", "version": "1.0.0", "description": "A Letterboxd clone built with React, Node.js, Express, and MongoDB", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm start", "build": "cd client && npm run build", "install-server": "cd server && npm install", "install-client": "cd client && npm install", "install-all": "npm run install-server && npm run install-client", "start": "cd server && npm start"}, "keywords": ["letterboxd", "movies", "react", "nodejs", "mongodb"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}