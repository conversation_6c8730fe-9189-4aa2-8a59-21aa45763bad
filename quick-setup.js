const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

console.log('🎬 Letterboxd Clone - Quick Setup');
console.log('==================================\n');

// Check if we're in the right directory
const currentDir = process.cwd();
console.log(`Current directory: ${currentDir}`);

// Check if package.json exists
if (!fs.existsSync('package.json')) {
  console.log('❌ package.json not found in current directory');
  console.log('Please navigate to the letterboxd-clone project directory first');
  console.log('\nExpected structure:');
  console.log('letterboxd-clone/');
  console.log('├── client/');
  console.log('├── server/');
  console.log('├── package.json');
  console.log('└── README.md');
  process.exit(1);
}

// Check if client and server directories exist
if (!fs.existsSync('client') || !fs.existsSync('server')) {
  console.log('❌ client/ or server/ directory not found');
  console.log('Please make sure you have the complete project structure');
  process.exit(1);
}

console.log('✅ Project structure looks good');

// Install root dependencies
console.log('\n📦 Installing root dependencies...');
const installRoot = spawn('npm', ['install'], { stdio: 'inherit', shell: true });

installRoot.on('close', (code) => {
  if (code !== 0) {
    console.log('❌ Failed to install root dependencies');
    process.exit(1);
  }
  
  console.log('✅ Root dependencies installed');
  
  // Install server dependencies
  console.log('\n📦 Installing server dependencies...');
  const installServer = spawn('npm', ['run', 'install-server'], { stdio: 'inherit', shell: true });
  
  installServer.on('close', (code) => {
    if (code !== 0) {
      console.log('❌ Failed to install server dependencies');
      process.exit(1);
    }
    
    console.log('✅ Server dependencies installed');
    
    // Install client dependencies
    console.log('\n📦 Installing client dependencies...');
    const installClient = spawn('npm', ['run', 'install-client'], { stdio: 'inherit', shell: true });
    
    installClient.on('close', (code) => {
      if (code !== 0) {
        console.log('❌ Failed to install client dependencies');
        process.exit(1);
      }
      
      console.log('✅ Client dependencies installed');
      console.log('\n🚀 Starting development servers...');
      console.log('Backend will run on: http://localhost:5000');
      console.log('Frontend will run on: http://localhost:3000');
      console.log('\nPress Ctrl+C to stop the servers\n');
      
      // Start development servers
      const dev = spawn('npm', ['run', 'dev'], { stdio: 'inherit', shell: true });
      
      process.on('SIGINT', () => {
        console.log('\n\n👋 Shutting down servers...');
        dev.kill('SIGINT');
        process.exit(0);
      });
    });
  });
});
