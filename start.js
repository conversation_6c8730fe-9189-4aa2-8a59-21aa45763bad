#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🎬 Starting Letterboxd Clone...\n');

// Check if dependencies are installed
const checkDependencies = () => {
  const serverNodeModules = path.join(__dirname, 'server', 'node_modules');
  const clientNodeModules = path.join(__dirname, 'client', 'node_modules');
  
  if (!fs.existsSync(serverNodeModules) || !fs.existsSync(clientNodeModules)) {
    console.log('📦 Installing dependencies...');
    console.log('This may take a few minutes...\n');
    
    const install = spawn('npm', ['run', 'install-all'], { 
      stdio: 'inherit',
      shell: true 
    });
    
    install.on('close', (code) => {
      if (code === 0) {
        console.log('\n✅ Dependencies installed successfully!');
        startServers();
      } else {
        console.error('\n❌ Failed to install dependencies');
        process.exit(1);
      }
    });
  } else {
    console.log('✅ Dependencies already installed');
    startServers();
  }
};

const startServers = () => {
  console.log('\n🚀 Starting development servers...');
  console.log('Backend: http://localhost:5000');
  console.log('Frontend: http://localhost:3000\n');
  
  const dev = spawn('npm', ['run', 'dev'], { 
    stdio: 'inherit',
    shell: true 
  });
  
  dev.on('close', (code) => {
    console.log(`\nServers stopped with code ${code}`);
  });
  
  // Handle Ctrl+C
  process.on('SIGINT', () => {
    console.log('\n\n👋 Shutting down servers...');
    dev.kill('SIGINT');
    process.exit(0);
  });
};

// Check environment files
const checkEnvFiles = () => {
  const serverEnv = path.join(__dirname, 'server', '.env');
  const clientEnv = path.join(__dirname, 'client', '.env');
  
  if (!fs.existsSync(serverEnv) || !fs.existsSync(clientEnv)) {
    console.log('❌ Environment files not found!');
    console.log('Please make sure .env files are configured properly.');
    process.exit(1);
  }
  
  console.log('✅ Environment files found');
};

// Main execution
console.log('🔍 Checking configuration...');
checkEnvFiles();
checkDependencies();
