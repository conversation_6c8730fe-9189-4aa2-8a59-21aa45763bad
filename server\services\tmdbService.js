const axios = require('axios');

class TMDBService {
  constructor() {
    this.apiKey = process.env.TMDB_API_KEY;
    this.baseURL = process.env.TMDB_BASE_URL || 'https://api.themoviedb.org/3';
    this.imageBaseURL = 'https://image.tmdb.org/t/p/';
    
    // Rate limiting - TMDB allows 40 requests per 10 seconds
    this.requestQueue = [];
    this.requestTimes = [];
    this.maxRequests = 35; // Leave some buffer
    this.timeWindow = 10000; // 10 seconds
    
    this.axiosInstance = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      params: {
        api_key: this.apiKey
      }
    });

    // Add request interceptor for rate limiting
    this.axiosInstance.interceptors.request.use(
      (config) => this.handleRateLimit(config),
      (error) => Promise.reject(error)
    );
  }

  async handleRateLimit(config) {
    const now = Date.now();
    
    // Remove old request times outside the window
    this.requestTimes = this.requestTimes.filter(time => now - time < this.timeWindow);
    
    // If we're at the limit, wait
    if (this.requestTimes.length >= this.maxRequests) {
      const oldestRequest = Math.min(...this.requestTimes);
      const waitTime = this.timeWindow - (now - oldestRequest) + 100; // Add 100ms buffer
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    this.requestTimes.push(now);
    return config;
  }

  // Search movies by title
  async searchMovies(query, page = 1) {
    try {
      const response = await this.axiosInstance.get('/search/movie', {
        params: {
          query,
          page,
          include_adult: false
        }
      });
      return response.data;
    } catch (error) {
      console.error('TMDB search error:', error.message);
      throw new Error('Failed to search movies');
    }
  }

  // Get popular movies
  async getPopularMovies(page = 1) {
    try {
      const response = await this.axiosInstance.get('/movie/popular', {
        params: { page }
      });
      return response.data;
    } catch (error) {
      console.error('TMDB popular movies error:', error.message);
      throw new Error('Failed to fetch popular movies');
    }
  }

  // Get movie details by ID
  async getMovieDetails(movieId) {
    try {
      const response = await this.axiosInstance.get(`/movie/${movieId}`, {
        params: {
          append_to_response: 'credits,videos,similar,reviews'
        }
      });
      return response.data;
    } catch (error) {
      console.error('TMDB movie details error:', error.message);
      throw new Error('Failed to fetch movie details');
    }
  }

  // Discover movies with filters
  async discoverMovies(filters = {}, page = 1) {
    try {
      const params = {
        page,
        include_adult: false,
        ...filters
      };

      const response = await this.axiosInstance.get('/discover/movie', {
        params
      });
      return response.data;
    } catch (error) {
      console.error('TMDB discover error:', error.message);
      throw new Error('Failed to discover movies');
    }
  }

  // Get movie genres
  async getGenres() {
    try {
      const response = await this.axiosInstance.get('/genre/movie/list');
      return response.data.genres;
    } catch (error) {
      console.error('TMDB genres error:', error.message);
      throw new Error('Failed to fetch genres');
    }
  }

  // Get trending movies
  async getTrendingMovies(timeWindow = 'week', page = 1) {
    try {
      const response = await this.axiosInstance.get(`/trending/movie/${timeWindow}`, {
        params: { page }
      });
      return response.data;
    } catch (error) {
      console.error('TMDB trending error:', error.message);
      throw new Error('Failed to fetch trending movies');
    }
  }

  // Get now playing movies
  async getNowPlayingMovies(page = 1) {
    try {
      const response = await this.axiosInstance.get('/movie/now_playing', {
        params: { page }
      });
      return response.data;
    } catch (error) {
      console.error('TMDB now playing error:', error.message);
      throw new Error('Failed to fetch now playing movies');
    }
  }

  // Get upcoming movies
  async getUpcomingMovies(page = 1) {
    try {
      const response = await this.axiosInstance.get('/movie/upcoming', {
        params: { page }
      });
      return response.data;
    } catch (error) {
      console.error('TMDB upcoming error:', error.message);
      throw new Error('Failed to fetch upcoming movies');
    }
  }

  // Get top rated movies
  async getTopRatedMovies(page = 1) {
    try {
      const response = await this.axiosInstance.get('/movie/top_rated', {
        params: { page }
      });
      return response.data;
    } catch (error) {
      console.error('TMDB top rated error:', error.message);
      throw new Error('Failed to fetch top rated movies');
    }
  }

  // Helper method to get full image URL
  getImageURL(path, size = 'w500') {
    if (!path) return null;
    return `${this.imageBaseURL}${size}${path}`;
  }

  // Helper method to format movie data
  formatMovieData(movie) {
    return {
      id: movie.id,
      title: movie.title,
      overview: movie.overview,
      posterPath: movie.poster_path,
      backdropPath: movie.backdrop_path,
      releaseDate: movie.release_date,
      voteAverage: movie.vote_average,
      voteCount: movie.vote_count,
      genreIds: movie.genre_ids,
      genres: movie.genres,
      runtime: movie.runtime,
      status: movie.status,
      tagline: movie.tagline,
      credits: movie.credits,
      videos: movie.videos,
      similar: movie.similar,
      reviews: movie.reviews
    };
  }
}

module.exports = new TMDBService();
