const express = require('express');
const { body, validationResult } = require('express-validator');
const { authenticateToken } = require('../middleware/auth');
const User = require('../models/User');
const Activity = require('../models/Activity');

const router = express.Router();

// @route   POST /api/auth/register
// @desc    Register/Update user after Firebase authentication
// @access  Private (requires Firebase token)
router.post('/register', 
  authenticateToken,
  [
    body('displayName')
      .trim()
      .isLength({ min: 1, max: 50 })
      .withMessage('Display name must be between 1 and 50 characters'),
    body('username')
      .optional()
      .trim()
      .isLength({ min: 3, max: 20 })
      .matches(/^[a-zA-Z0-9_]+$/)
      .withMessage('Username must be 3-20 characters and contain only letters, numbers, and underscores'),
    body('bio')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Bio must be less than 500 characters')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { displayName, username, bio } = req.body;
      const user = req.user;

      // Check if username is already taken (if provided)
      if (username && username !== user.username) {
        const existingUser = await User.findOne({ 
          username: username.toLowerCase(),
          _id: { $ne: user._id }
        });
        if (existingUser) {
          return res.status(400).json({ message: 'Username already taken' });
        }
      }

      // Update user information
      user.displayName = displayName;
      if (username) user.username = username.toLowerCase();
      if (bio !== undefined) user.bio = bio;
      user.lastActive = new Date();

      await user.save();

      res.json({
        message: 'User updated successfully',
        user: {
          id: user._id,
          firebaseUid: user.firebaseUid,
          email: user.email,
          displayName: user.displayName,
          username: user.username,
          bio: user.bio,
          photoURL: user.photoURL,
          favoriteFilms: user.favoriteFilms,
          stats: user.stats,
          preferences: user.preferences
        }
      });
    } catch (error) {
      console.error('Registration error:', error);
      res.status(500).json({ message: 'Server error during registration' });
    }
  }
);

// @route   GET /api/auth/me
// @desc    Get current user profile
// @access  Private
router.get('/me', authenticateToken, async (req, res) => {
  try {
    const user = await User.findById(req.user._id)
      .populate('followers', 'displayName username photoURL')
      .populate('following', 'displayName username photoURL');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Update last active
    user.lastActive = new Date();
    await user.save();

    res.json({
      id: user._id,
      firebaseUid: user.firebaseUid,
      email: user.email,
      displayName: user.displayName,
      username: user.username,
      bio: user.bio,
      photoURL: user.photoURL,
      favoriteFilms: user.favoriteFilms,
      followers: user.followers,
      following: user.following,
      stats: user.stats,
      preferences: user.preferences,
      watchedFilms: user.watchedFilms.slice(-10), // Last 10 watched films
      createdAt: user.createdAt,
      lastActive: user.lastActive
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   PUT /api/auth/profile
// @desc    Update user profile
// @access  Private
router.put('/profile',
  authenticateToken,
  [
    body('displayName')
      .optional()
      .trim()
      .isLength({ min: 1, max: 50 })
      .withMessage('Display name must be between 1 and 50 characters'),
    body('username')
      .optional()
      .trim()
      .isLength({ min: 3, max: 20 })
      .matches(/^[a-zA-Z0-9_]+$/)
      .withMessage('Username must be 3-20 characters and contain only letters, numbers, and underscores'),
    body('bio')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Bio must be less than 500 characters'),
    body('favoriteFilms')
      .optional()
      .isArray({ max: 4 })
      .withMessage('You can have maximum 4 favorite films')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { displayName, username, bio, favoriteFilms, preferences } = req.body;
      const user = req.user;

      // Check if username is already taken (if provided and different)
      if (username && username !== user.username) {
        const existingUser = await User.findOne({ 
          username: username.toLowerCase(),
          _id: { $ne: user._id }
        });
        if (existingUser) {
          return res.status(400).json({ message: 'Username already taken' });
        }
      }

      // Update fields if provided
      if (displayName) user.displayName = displayName;
      if (username) user.username = username.toLowerCase();
      if (bio !== undefined) user.bio = bio;
      if (favoriteFilms) user.favoriteFilms = favoriteFilms;
      if (preferences) {
        user.preferences = { ...user.preferences, ...preferences };
      }

      await user.save();

      res.json({
        message: 'Profile updated successfully',
        user: {
          id: user._id,
          displayName: user.displayName,
          username: user.username,
          bio: user.bio,
          favoriteFilms: user.favoriteFilms,
          preferences: user.preferences
        }
      });
    } catch (error) {
      console.error('Profile update error:', error);
      res.status(500).json({ message: 'Server error during profile update' });
    }
  }
);

// @route   DELETE /api/auth/account
// @desc    Delete user account
// @access  Private
router.delete('/account', authenticateToken, async (req, res) => {
  try {
    const userId = req.user._id;

    // Delete user's reviews, lists, and activities
    await Promise.all([
      require('../models/Review').deleteMany({ user: userId }),
      require('../models/List').deleteMany({ user: userId }),
      Activity.deleteMany({ user: userId })
    ]);

    // Remove user from other users' followers/following lists
    await User.updateMany(
      { $or: [{ followers: userId }, { following: userId }] },
      { $pull: { followers: userId, following: userId } }
    );

    // Delete the user
    await User.findByIdAndDelete(userId);

    res.json({ message: 'Account deleted successfully' });
  } catch (error) {
    console.error('Account deletion error:', error);
    res.status(500).json({ message: 'Server error during account deletion' });
  }
});

module.exports = router;
