const express = require('express');
const { query, validationResult } = require('express-validator');
const { authenticateToken, optionalAuth } = require('../middleware/auth');
const User = require('../models/User');
const Review = require('../models/Review');
const List = require('../models/List');

const router = express.Router();

// @route   GET /api/users/search
// @desc    Search users by display name or username
// @access  Public
router.get('/search',
  [
    query('q')
      .trim()
      .isLength({ min: 1 })
      .withMessage('Search query is required'),
    query('page')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Page must be between 1 and 1000'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 50 })
      .withMessage('Limit must be between 1 and 50')
  ],
  optionalAuth,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { q: query, page = 1, limit = 20 } = req.query;
      const skip = (parseInt(page) - 1) * parseInt(limit);

      const searchQuery = {
        $text: { $search: query },
        isActive: true,
        'preferences.profileVisibility': 'public'
      };

      const [users, total] = await Promise.all([
        User.find(searchQuery)
          .select('displayName username photoURL bio favoriteFilms stats')
          .sort({ score: { $meta: 'textScore' } })
          .skip(skip)
          .limit(parseInt(limit)),
        User.countDocuments(searchQuery)
      ]);

      res.json({
        users,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });
    } catch (error) {
      console.error('Search users error:', error);
      res.status(500).json({ message: 'Server error searching users' });
    }
  }
);

// @route   GET /api/users/:id
// @desc    Get user profile by ID
// @access  Public
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const user = await User.findById(req.params.id)
      .select('-firebaseUid -email -emailVerified')
      .populate('followers', 'displayName username photoURL')
      .populate('following', 'displayName username photoURL');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if profile is private and user is not the owner
    if (user.preferences.profileVisibility === 'private' && 
        (!req.user || user._id.toString() !== req.user._id.toString())) {
      return res.status(403).json({ message: 'Profile is private' });
    }

    // Get user's recent reviews
    const recentReviews = await Review.find({
      user: user._id,
      visibility: 'public'
    })
    .populate('user', 'displayName username photoURL')
    .sort({ createdAt: -1 })
    .limit(5);

    // Get user's public lists
    const publicLists = await List.find({
      user: user._id,
      visibility: 'public',
      isWatchlist: false
    })
    .populate('user', 'displayName username photoURL')
    .sort({ createdAt: -1 })
    .limit(5);

    // Check if current user is following this user
    let isFollowing = false;
    if (req.user) {
      isFollowing = user.followers.some(follower => 
        follower._id.toString() === req.user._id.toString()
      );
    }

    res.json({
      ...user.toObject(),
      recentReviews,
      publicLists,
      isFollowing
    });
  } catch (error) {
    console.error('Get user profile error:', error);
    res.status(500).json({ message: 'Server error fetching user profile' });
  }
});

// @route   GET /api/users/:id/reviews
// @desc    Get user's reviews
// @access  Public
router.get('/:id/reviews',
  [
    query('page')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Page must be between 1 and 1000'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 50 })
      .withMessage('Limit must be between 1 and 50'),
    query('sort')
      .optional()
      .isIn(['newest', 'oldest', 'rating_high', 'rating_low'])
      .withMessage('Invalid sort option')
  ],
  optionalAuth,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { page = 1, limit = 20, sort = 'newest' } = req.query;
      const skip = (parseInt(page) - 1) * parseInt(limit);

      // Build query - only show public reviews unless it's the user's own profile
      const query = { user: req.params.id };
      if (!req.user || req.user._id.toString() !== req.params.id) {
        query.visibility = 'public';
      }

      // Build sort
      let sortQuery = {};
      switch (sort) {
        case 'oldest':
          sortQuery = { createdAt: 1 };
          break;
        case 'rating_high':
          sortQuery = { rating: -1, createdAt: -1 };
          break;
        case 'rating_low':
          sortQuery = { rating: 1, createdAt: -1 };
          break;
        default:
          sortQuery = { createdAt: -1 };
      }

      const [reviews, total] = await Promise.all([
        Review.find(query)
          .populate('user', 'displayName username photoURL')
          .sort(sortQuery)
          .skip(skip)
          .limit(parseInt(limit)),
        Review.countDocuments(query)
      ]);

      res.json({
        reviews,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });
    } catch (error) {
      console.error('Get user reviews error:', error);
      res.status(500).json({ message: 'Server error fetching user reviews' });
    }
  }
);

// @route   GET /api/users/:id/lists
// @desc    Get user's lists
// @access  Public
router.get('/:id/lists',
  [
    query('page')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Page must be between 1 and 1000'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 50 })
      .withMessage('Limit must be between 1 and 50'),
    query('sort')
      .optional()
      .isIn(['newest', 'oldest', 'most_liked', 'most_movies'])
      .withMessage('Invalid sort option')
  ],
  optionalAuth,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { page = 1, limit = 20, sort = 'newest' } = req.query;
      const skip = (parseInt(page) - 1) * parseInt(limit);

      // Build query - only show public lists unless it's the user's own profile
      const query = { 
        user: req.params.id,
        isWatchlist: false
      };
      
      if (!req.user || req.user._id.toString() !== req.params.id) {
        query.visibility = 'public';
      }

      // Build sort
      let sortQuery = {};
      switch (sort) {
        case 'oldest':
          sortQuery = { createdAt: 1 };
          break;
        case 'most_liked':
          sortQuery = { 'stats.likesCount': -1, createdAt: -1 };
          break;
        case 'most_movies':
          sortQuery = { 'stats.moviesCount': -1, createdAt: -1 };
          break;
        default:
          sortQuery = { createdAt: -1 };
      }

      const [lists, total] = await Promise.all([
        List.find(query)
          .populate('user', 'displayName username photoURL')
          .sort(sortQuery)
          .skip(skip)
          .limit(parseInt(limit)),
        List.countDocuments(query)
      ]);

      res.json({
        lists,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });
    } catch (error) {
      console.error('Get user lists error:', error);
      res.status(500).json({ message: 'Server error fetching user lists' });
    }
  }
);

// @route   GET /api/users/:id/watchlist
// @desc    Get user's watchlist (if public or own)
// @access  Public
router.get('/:id/watchlist', optionalAuth, async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if user can view watchlist
    const isOwner = req.user && req.user._id.toString() === req.params.id;
    if (!isOwner && !user.preferences.showWatchlist) {
      return res.status(403).json({ message: 'Watchlist is private' });
    }

    const watchlist = await List.findOne({
      user: req.params.id,
      isWatchlist: true
    }).populate('user', 'displayName username photoURL');

    if (!watchlist) {
      return res.status(404).json({ message: 'Watchlist not found' });
    }

    res.json(watchlist);
  } catch (error) {
    console.error('Get user watchlist error:', error);
    res.status(500).json({ message: 'Server error fetching watchlist' });
  }
});

module.exports = router;
