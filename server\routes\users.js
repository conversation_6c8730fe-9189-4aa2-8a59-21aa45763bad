const express = require('express');
const { query, validationResult } = require('express-validator');
const { authenticateToken, optionalAuth } = require('../middleware/auth');
const firestoreService = require('../services/firestoreService');

const router = express.Router();

// @route   GET /api/users/search
// @desc    Search users by display name or username
// @access  Public
router.get('/search',
  [
    query('q')
      .trim()
      .isLength({ min: 1 })
      .withMessage('Search query is required'),
    query('page')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Page must be between 1 and 1000'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 50 })
      .withMessage('Limit must be between 1 and 50')
  ],
  optionalAuth,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { q: query, page = 1, limit = 20 } = req.query;
      const skip = (parseInt(page) - 1) * parseInt(limit);

      const filters = [
        ['isActive', '==', true],
        ['preferences.profileVisibility', '==', 'public']
      ];

      // Simple search by displayName (Firestore doesn't have full-text search like MongoDB)
      const allUsers = await firestoreService.query('users', filters);
      const filteredUsers = allUsers.filter(user =>
        user.displayName?.toLowerCase().includes(query.toLowerCase()) ||
        user.username?.toLowerCase().includes(query.toLowerCase())
      );

      const total = filteredUsers.length;
      const users = filteredUsers
        .slice(skip, skip + parseInt(limit))
        .map(user => ({
          id: user.id,
          displayName: user.displayName,
          username: user.username,
          photoURL: user.photoURL,
          bio: user.bio,
          favoriteFilms: user.favoriteFilms,
          stats: user.stats
        }));

      res.json({
        users,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });
    } catch (error) {
      console.error('Search users error:', error);
      res.status(500).json({ message: 'Server error searching users' });
    }
  }
);

// @route   GET /api/users/:id
// @desc    Get user profile by ID
// @access  Public
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const user = await firestoreService.getById('users', req.params.id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if profile is private and user is not the owner
    if (user.preferences?.profileVisibility === 'private' &&
        (!req.user || user.id !== req.user.id)) {
      return res.status(403).json({ message: 'Profile is private' });
    }

    // Get user's recent reviews
    const recentReviews = await firestoreService.query('reviews', [
      ['userId', '==', user.id],
      ['visibility', '==', 'public']
    ], ['createdAt', 'desc'], 5);

    // Get user's public lists
    const publicLists = await firestoreService.query('lists', [
      ['userId', '==', user.id],
      ['visibility', '==', 'public'],
      ['isWatchlist', '==', false]
    ], ['createdAt', 'desc'], 5);

    // Check if current user is following this user
    let isFollowing = false;
    if (req.user && user.followers) {
      isFollowing = user.followers.includes(req.user.id);
    }

    // Remove sensitive data
    const { firebaseUid, email, emailVerified, ...publicUserData } = user;

    res.json({
      ...publicUserData,
      recentReviews,
      publicLists,
      isFollowing
    });
  } catch (error) {
    console.error('Get user profile error:', error);
    res.status(500).json({ message: 'Server error fetching user profile' });
  }
});

// @route   GET /api/users/:id/reviews
// @desc    Get user's reviews
// @access  Public
router.get('/:id/reviews',
  [
    query('page')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Page must be between 1 and 1000'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 50 })
      .withMessage('Limit must be between 1 and 50'),
    query('sort')
      .optional()
      .isIn(['newest', 'oldest', 'rating_high', 'rating_low'])
      .withMessage('Invalid sort option')
  ],
  optionalAuth,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { page = 1, limit = 20, sort = 'newest' } = req.query;
      const skip = (parseInt(page) - 1) * parseInt(limit);

      // Build filters - only show public reviews unless it's the user's own profile
      const filters = [['userId', '==', req.params.id]];
      if (!req.user || req.user.id !== req.params.id) {
        filters.push(['visibility', '==', 'public']);
      }

      // Build sort
      let orderBy = ['createdAt', 'desc'];
      switch (sort) {
        case 'oldest':
          orderBy = ['createdAt', 'asc'];
          break;
        case 'rating_high':
          orderBy = ['rating', 'desc'];
          break;
        case 'rating_low':
          orderBy = ['rating', 'asc'];
          break;
        default:
          orderBy = ['createdAt', 'desc'];
      }

      const allReviews = await firestoreService.query('reviews', filters, orderBy);
      const total = allReviews.length;
      const reviews = allReviews.slice(skip, skip + parseInt(limit));

      res.json({
        reviews,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });
    } catch (error) {
      console.error('Get user reviews error:', error);
      res.status(500).json({ message: 'Server error fetching user reviews' });
    }
  }
);

// @route   GET /api/users/:id/lists
// @desc    Get user's lists
// @access  Public
router.get('/:id/lists',
  [
    query('page')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Page must be between 1 and 1000'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 50 })
      .withMessage('Limit must be between 1 and 50'),
    query('sort')
      .optional()
      .isIn(['newest', 'oldest', 'most_liked', 'most_movies'])
      .withMessage('Invalid sort option')
  ],
  optionalAuth,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { page = 1, limit = 20, sort = 'newest' } = req.query;
      const skip = (parseInt(page) - 1) * parseInt(limit);

      // Build filters - only show public lists unless it's the user's own profile
      const filters = [
        ['userId', '==', req.params.id],
        ['isWatchlist', '==', false]
      ];

      if (!req.user || req.user.id !== req.params.id) {
        filters.push(['visibility', '==', 'public']);
      }

      // Build sort
      let orderBy = ['createdAt', 'desc'];
      switch (sort) {
        case 'oldest':
          orderBy = ['createdAt', 'asc'];
          break;
        case 'most_liked':
          orderBy = ['stats.likesCount', 'desc'];
          break;
        case 'most_movies':
          orderBy = ['stats.moviesCount', 'desc'];
          break;
        default:
          orderBy = ['createdAt', 'desc'];
      }

      const allLists = await firestoreService.query('lists', filters, orderBy);
      const total = allLists.length;
      const lists = allLists.slice(skip, skip + parseInt(limit));

      res.json({
        lists,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });
    } catch (error) {
      console.error('Get user lists error:', error);
      res.status(500).json({ message: 'Server error fetching user lists' });
    }
  }
);

// @route   GET /api/users/:id/watchlist
// @desc    Get user's watchlist (if public or own)
// @access  Public
router.get('/:id/watchlist', optionalAuth, async (req, res) => {
  try {
    const user = await firestoreService.getById('users', req.params.id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if user can view watchlist
    const isOwner = req.user && req.user.id === req.params.id;
    if (!isOwner && !user.preferences?.showWatchlist) {
      return res.status(403).json({ message: 'Watchlist is private' });
    }

    const watchlists = await firestoreService.query('lists', [
      ['userId', '==', req.params.id],
      ['isWatchlist', '==', true]
    ]);

    if (watchlists.length === 0) {
      return res.status(404).json({ message: 'Watchlist not found' });
    }

    res.json(watchlists[0]);
  } catch (error) {
    console.error('Get user watchlist error:', error);
    res.status(500).json({ message: 'Server error fetching watchlist' });
  }
});

module.exports = router;
