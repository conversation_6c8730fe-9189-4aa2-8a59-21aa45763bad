const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { authenticateToken, optionalAuth } = require('../middleware/auth');
const firestoreService = require('../services/firestoreService');

const router = express.Router();

// @route   POST /api/lists
// @desc    Create a new list
// @access  Private
router.post('/',
  authenticateToken,
  [
    body('name')
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('List name is required and must be less than 100 characters'),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 1000 })
      .withMessage('Description must be less than 1000 characters'),
    body('visibility')
      .optional()
      .isIn(['public', 'private'])
      .withMessage('Visibility must be public or private'),
    body('isWatchlist')
      .optional()
      .isBoolean()
      .withMessage('Is watchlist must be a boolean'),
    body('tags')
      .optional()
      .isArray({ max: 10 })
      .withMessage('Maximum 10 tags allowed')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const {
        name,
        description = '',
        visibility = 'public',
        isWatchlist = false,
        tags = []
      } = req.body;

      // Check if user already has a watchlist (only one allowed)
      if (isWatchlist) {
        const existingWatchlist = await List.findOne({
          user: req.user._id,
          isWatchlist: true
        });

        if (existingWatchlist) {
          return res.status(400).json({ message: 'You already have a watchlist' });
        }
      }

      // Create new list
      const list = new List({
        user: req.user._id,
        name,
        description,
        visibility,
        isWatchlist,
        tags: tags.map(tag => tag.toLowerCase().trim())
      });

      await list.save();

      // Update user stats
      await User.findByIdAndUpdate(req.user._id, {
        $inc: { 'stats.totalLists': 1 }
      });

      // Create activity for public lists
      if (visibility === 'public' && !isWatchlist) {
        await Activity.create({
          user: req.user._id,
          type: 'list_created',
          list: list._id,
          metadata: {
            listName: name
          }
        });
      }

      // Populate user data for response
      await list.populate('user', 'displayName username photoURL');

      res.status(201).json({
        message: 'List created successfully',
        list
      });
    } catch (error) {
      console.error('Create list error:', error);
      res.status(500).json({ message: 'Server error creating list' });
    }
  }
);

// @route   GET /api/lists
// @desc    Get lists with filters
// @access  Public
router.get('/',
  [
    query('page')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Page must be between 1 and 1000'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 50 })
      .withMessage('Limit must be between 1 and 50'),
    query('user')
      .optional()
      .isMongoId()
      .withMessage('Valid user ID required'),
    query('sort')
      .optional()
      .isIn(['newest', 'oldest', 'most_liked', 'most_movies'])
      .withMessage('Invalid sort option'),
    query('search')
      .optional()
      .trim()
      .isLength({ min: 1 })
      .withMessage('Search query must not be empty')
  ],
  optionalAuth,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const {
        page = 1,
        limit = 20,
        user: userId,
        sort = 'newest',
        search
      } = req.query;

      // Build query
      const query = { visibility: 'public', isWatchlist: false };
      
      if (userId) query.user = userId;
      if (search) {
        query.$text = { $search: search };
      }

      // Build sort
      let sortQuery = {};
      switch (sort) {
        case 'oldest':
          sortQuery = { createdAt: 1 };
          break;
        case 'most_liked':
          sortQuery = { 'stats.likesCount': -1, createdAt: -1 };
          break;
        case 'most_movies':
          sortQuery = { 'stats.moviesCount': -1, createdAt: -1 };
          break;
        default:
          sortQuery = { createdAt: -1 };
      }

      const skip = (parseInt(page) - 1) * parseInt(limit);

      const [lists, total] = await Promise.all([
        List.find(query)
          .populate('user', 'displayName username photoURL')
          .sort(sortQuery)
          .skip(skip)
          .limit(parseInt(limit)),
        List.countDocuments(query)
      ]);

      res.json({
        lists,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });
    } catch (error) {
      console.error('Get lists error:', error);
      res.status(500).json({ message: 'Server error fetching lists' });
    }
  }
);

// @route   GET /api/lists/watchlist
// @desc    Get user's watchlist
// @access  Private
router.get('/watchlist', authenticateToken, async (req, res) => {
  try {
    const watchlist = await List.findOne({
      user: req.user._id,
      isWatchlist: true
    }).populate('user', 'displayName username photoURL');

    if (!watchlist) {
      // Create watchlist if it doesn't exist
      const newWatchlist = new List({
        user: req.user._id,
        name: 'Watchlist',
        description: 'Movies I want to watch',
        visibility: 'private',
        isWatchlist: true
      });

      await newWatchlist.save();
      await newWatchlist.populate('user', 'displayName username photoURL');
      
      return res.json(newWatchlist);
    }

    res.json(watchlist);
  } catch (error) {
    console.error('Get watchlist error:', error);
    res.status(500).json({ message: 'Server error fetching watchlist' });
  }
);

// @route   GET /api/lists/:id
// @desc    Get list by ID
// @access  Public
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const list = await List.findById(req.params.id)
      .populate('user', 'displayName username photoURL')
      .populate('comments.user', 'displayName username photoURL');

    if (!list) {
      return res.status(404).json({ message: 'List not found' });
    }

    // Check if list is private and user is not the owner
    if (list.visibility === 'private' && (!req.user || list.user._id.toString() !== req.user._id.toString())) {
      return res.status(403).json({ message: 'Access denied' });
    }

    res.json(list);
  } catch (error) {
    console.error('Get list error:', error);
    res.status(500).json({ message: 'Server error fetching list' });
  }
);

// @route   PUT /api/lists/:id
// @desc    Update list
// @access  Private (owner only)
router.put('/:id',
  authenticateToken,
  [
    body('name')
      .optional()
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('List name must be between 1 and 100 characters'),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 1000 })
      .withMessage('Description must be less than 1000 characters'),
    body('visibility')
      .optional()
      .isIn(['public', 'private'])
      .withMessage('Visibility must be public or private'),
    body('tags')
      .optional()
      .isArray({ max: 10 })
      .withMessage('Maximum 10 tags allowed')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const list = await List.findById(req.params.id);

      if (!list) {
        return res.status(404).json({ message: 'List not found' });
      }

      // Check if user owns the list
      if (list.user.toString() !== req.user._id.toString()) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const { name, description, visibility, tags } = req.body;

      // Update fields if provided
      if (name !== undefined) list.name = name;
      if (description !== undefined) list.description = description;
      if (visibility !== undefined) list.visibility = visibility;
      if (tags !== undefined) list.tags = tags.map(tag => tag.toLowerCase().trim());

      await list.save();
      await list.populate('user', 'displayName username photoURL');

      // Create activity for public list updates
      if (list.visibility === 'public' && !list.isWatchlist) {
        await Activity.create({
          user: req.user._id,
          type: 'list_updated',
          list: list._id,
          metadata: {
            listName: list.name
          }
        });
      }

      res.json({
        message: 'List updated successfully',
        list
      });
    } catch (error) {
      console.error('Update list error:', error);
      res.status(500).json({ message: 'Server error updating list' });
    }
  }
);

// @route   DELETE /api/lists/:id
// @desc    Delete list
// @access  Private (owner only)
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const list = await List.findById(req.params.id);

    if (!list) {
      return res.status(404).json({ message: 'List not found' });
    }

    // Check if user owns the list
    if (list.user.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Don't allow deletion of watchlist
    if (list.isWatchlist) {
      return res.status(400).json({ message: 'Cannot delete watchlist' });
    }

    await List.findByIdAndDelete(req.params.id);

    // Update user stats
    await User.findByIdAndUpdate(req.user._id, {
      $inc: { 'stats.totalLists': -1 }
    });

    // Delete related activities
    await Activity.deleteMany({ list: req.params.id });

    res.json({ message: 'List deleted successfully' });
  } catch (error) {
    console.error('Delete list error:', error);
    res.status(500).json({ message: 'Server error deleting list' });
  }
});

// @route   POST /api/lists/:id/movies
// @desc    Add movie to list
// @access  Private (owner only)
router.post('/:id/movies',
  authenticateToken,
  [
    body('tmdbId')
      .isInt({ min: 1 })
      .withMessage('Valid TMDB ID is required'),
    body('title')
      .trim()
      .isLength({ min: 1, max: 200 })
      .withMessage('Movie title is required'),
    body('notes')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Notes must be less than 500 characters')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const list = await List.findById(req.params.id);

      if (!list) {
        return res.status(404).json({ message: 'List not found' });
      }

      // Check if user owns the list
      if (list.user.toString() !== req.user._id.toString()) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const { tmdbId, title, posterPath, releaseDate, notes } = req.body;

      // Check if movie is already in the list
      const existingMovie = list.movies.find(movie => movie.tmdbId === tmdbId);
      if (existingMovie) {
        return res.status(400).json({ message: 'Movie already in list' });
      }

      list.addMovie({
        tmdbId,
        title,
        posterPath,
        releaseDate,
        notes
      });

      await list.save();

      res.json({
        message: 'Movie added to list successfully',
        list
      });
    } catch (error) {
      console.error('Add movie to list error:', error);
      res.status(500).json({ message: 'Server error adding movie to list' });
    }
  }
);

// @route   DELETE /api/lists/:id/movies/:movieId
// @desc    Remove movie from list
// @access  Private (owner only)
router.delete('/:id/movies/:movieId', authenticateToken, async (req, res) => {
  try {
    const list = await List.findById(req.params.id);

    if (!list) {
      return res.status(404).json({ message: 'List not found' });
    }

    // Check if user owns the list
    if (list.user.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const tmdbId = parseInt(req.params.movieId);
    list.removeMovie(tmdbId);

    await list.save();

    res.json({
      message: 'Movie removed from list successfully',
      list
    });
  } catch (error) {
    console.error('Remove movie from list error:', error);
    res.status(500).json({ message: 'Server error removing movie from list' });
  }
});

// @route   PUT /api/lists/:id/movies/reorder
// @desc    Reorder movies in list
// @access  Private (owner only)
router.put('/:id/movies/reorder',
  authenticateToken,
  [
    body('movieOrders')
      .isArray()
      .withMessage('Movie orders must be an array'),
    body('movieOrders.*.tmdbId')
      .isInt({ min: 1 })
      .withMessage('Valid TMDB ID is required'),
    body('movieOrders.*.order')
      .isInt({ min: 0 })
      .withMessage('Order must be a non-negative integer')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const list = await List.findById(req.params.id);

      if (!list) {
        return res.status(404).json({ message: 'List not found' });
      }

      // Check if user owns the list
      if (list.user.toString() !== req.user._id.toString()) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const { movieOrders } = req.body;
      list.reorderMovies(movieOrders);

      await list.save();

      res.json({
        message: 'Movies reordered successfully',
        list
      });
    } catch (error) {
      console.error('Reorder movies error:', error);
      res.status(500).json({ message: 'Server error reordering movies' });
    }
  }
);

// @route   POST /api/lists/:id/like
// @desc    Like/unlike a list
// @access  Private
router.post('/:id/like', authenticateToken, async (req, res) => {
  try {
    const list = await List.findById(req.params.id);

    if (!list) {
      return res.status(404).json({ message: 'List not found' });
    }

    // Can't like private lists or watchlists
    if (list.visibility === 'private' || list.isWatchlist) {
      return res.status(400).json({ message: 'Cannot like this list' });
    }

    const isLiked = list.isLikedBy(req.user._id);

    if (isLiked) {
      list.removeLike(req.user._id);
    } else {
      list.addLike(req.user._id);

      // Create activity if liking someone else's list
      if (list.user.toString() !== req.user._id.toString()) {
        await Activity.create({
          user: req.user._id,
          type: 'list_liked',
          targetUser: list.user,
          list: list._id,
          metadata: {
            listName: list.name
          }
        });
      }
    }

    await list.save();

    res.json({
      message: isLiked ? 'List unliked' : 'List liked',
      isLiked: !isLiked,
      likesCount: list.likes.length
    });
  } catch (error) {
    console.error('Like list error:', error);
    res.status(500).json({ message: 'Server error liking list' });
  }
});

module.exports = router;
