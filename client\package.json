{"name": "letterboxd-clone-client", "version": "1.0.0", "description": "Frontend client for Letterboxd clone", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "firebase": "^10.7.1", "axios": "^1.6.2", "react-icons": "^4.12.0", "react-infinite-scroll-component": "^6.1.0", "react-loading-skeleton": "^3.3.1", "react-hot-toast": "^2.4.1", "date-fns": "^2.30.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"react-scripts": "5.0.1", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10"}, "proxy": "http://localhost:5000"}