@echo off
echo 🎬 Letterboxd Clone - Quick Start
echo ================================
echo.

REM Check if we're in the right directory
if not exist package.json (
    echo ❌ package.json not found in current directory
    echo Please navigate to the letterboxd-clone project directory first
    echo.
    echo Expected structure:
    echo letterboxd-clone/
    echo ├── client/
    echo ├── server/
    echo ├── package.json
    echo └── README.md
    pause
    exit /b 1
)

if not exist client (
    echo ❌ client/ directory not found
    echo Please make sure you have the complete project structure
    pause
    exit /b 1
)

if not exist server (
    echo ❌ server/ directory not found
    echo Please make sure you have the complete project structure
    pause
    exit /b 1
)

echo ✅ Project structure looks good
echo.

echo 📦 Installing dependencies...
echo This may take a few minutes...
echo.

REM Install root dependencies
echo Installing root dependencies...
call npm install
if errorlevel 1 (
    echo ❌ Failed to install root dependencies
    pause
    exit /b 1
)

REM Install server dependencies
echo Installing server dependencies...
call npm run install-server
if errorlevel 1 (
    echo ❌ Failed to install server dependencies
    pause
    exit /b 1
)

REM Install client dependencies
echo Installing client dependencies...
call npm run install-client
if errorlevel 1 (
    echo ❌ Failed to install client dependencies
    pause
    exit /b 1
)

echo.
echo ✅ All dependencies installed successfully!
echo.
echo 🚀 Starting development servers...
echo Backend: http://localhost:5000
echo Frontend: http://localhost:3000
echo.
echo Press Ctrl+C to stop the servers
echo.

REM Install concurrently if not present
echo Installing concurrently...
call npm install concurrently

REM Start development servers
echo Starting both servers...
call npm run dev

REM If that fails, try alternative method
if errorlevel 1 (
    echo.
    echo ❌ Concurrent start failed. Starting servers separately...
    echo.
    echo Please open a new Command Prompt and run:
    echo cd server
    echo npm run dev
    echo.
    echo Then in another Command Prompt run:
    echo cd client
    echo npm start
    pause
)
