import React from 'react';
import { useSearchParams } from 'react-router-dom';

const SearchPage = () => {
  const [searchParams] = useSearchParams();
  const query = searchParams.get('q');

  return (
    <div className="min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 className="text-3xl font-bold mb-8">Search Results</h1>
        {query && <p className="text-gray-400">Search query: "{query}"</p>}
        <p className="text-gray-400 mt-4">This page will show search results for movies, users, and lists.</p>
      </div>
    </div>
  );
};

export default SearchPage;
