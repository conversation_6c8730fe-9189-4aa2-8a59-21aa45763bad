const mongoose = require('mongoose');

const reviewSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  tmdbId: {
    type: Number,
    required: true,
    index: true
  },
  movieTitle: {
    type: String,
    required: true
  },
  moviePosterPath: String,
  movieReleaseDate: String,
  rating: {
    type: Number,
    min: 0.5,
    max: 5,
    validate: {
      validator: function(v) {
        return v % 0.5 === 0; // Only allow half-star increments
      },
      message: 'Rating must be in half-star increments (0.5, 1, 1.5, etc.)'
    }
  },
  reviewText: {
    type: String,
    maxlength: 2000,
    trim: true
  },
  containsSpoilers: {
    type: Boolean,
    default: false
  },
  likes: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    likedAt: {
      type: Date,
      default: Date.now
    }
  }],
  comments: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    text: {
      type: String,
      required: true,
      maxlength: 500,
      trim: true
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  watchedDate: {
    type: Date,
    default: Date.now
  },
  isRewatch: {
    type: Boolean,
    default: false
  },
  visibility: {
    type: String,
    enum: ['public', 'private', 'friends'],
    default: 'public'
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  stats: {
    likesCount: {
      type: Number,
      default: 0
    },
    commentsCount: {
      type: Number,
      default: 0
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound indexes for efficient queries
reviewSchema.index({ user: 1, tmdbId: 1 }, { unique: true }); // One review per user per movie
reviewSchema.index({ tmdbId: 1, createdAt: -1 });
reviewSchema.index({ user: 1, createdAt: -1 });
reviewSchema.index({ rating: 1 });
reviewSchema.index({ 'likes.user': 1 });
reviewSchema.index({ visibility: 1, createdAt: -1 });

// Text search index
reviewSchema.index({ 
  movieTitle: 'text', 
  reviewText: 'text', 
  tags: 'text' 
});

// Virtual for likes count
reviewSchema.virtual('likesCount').get(function() {
  return this.likes.length;
});

// Virtual for comments count
reviewSchema.virtual('commentsCount').get(function() {
  return this.comments.length;
});

// Pre-save middleware to update stats
reviewSchema.pre('save', function(next) {
  this.stats.likesCount = this.likes.length;
  this.stats.commentsCount = this.comments.length;
  next();
});

// Method to check if user has liked the review
reviewSchema.methods.isLikedBy = function(userId) {
  return this.likes.some(like => like.user.toString() === userId.toString());
};

// Method to add like
reviewSchema.methods.addLike = function(userId) {
  if (!this.isLikedBy(userId)) {
    this.likes.push({ user: userId });
  }
};

// Method to remove like
reviewSchema.methods.removeLike = function(userId) {
  this.likes = this.likes.filter(like => like.user.toString() !== userId.toString());
};

module.exports = mongoose.model('Review', reviewSchema);
